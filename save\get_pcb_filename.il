;; =========================================================================
;; Get PCB File Name Script for Cadence Allegro
;; - Gets and displays the current PCB file name
;; - Shows design type and full path information
;; - Works in PCB Editor mode
;; =========================================================================

axlCmdRegister("get_pcb_filename" 'get_pcb_filename)

procedure(get_pcb_filename()
  prog((design_name design_type full_path working_dir)
    ; Check if a design is currently open
    unless(axlIsDesignOpen()
      axlUIConfirm("No design is currently open.")
      println("No design is currently open.")
      return(nil)
    )
    
    ; Get the current design nameax
    design_name = lCurrentDesign()
    
    ; Get the design type
    design_type = axlDesignType(t)
    
    ; Get the working directory
    working_dir = axlGetVariable("WORKINGDIR")
    
    ; Construct full path
    full_path = strcat(working_dir "/" design_name)
    
    ; Display header
    println("Current PCB Design Information:")
    println("==============================")
    
    ; Display the information
    printf("Design Name: %s\n" design_name)
    printf("Design Type: %s\n" design_type)
    printf("Working Directory: %s\n" working_dir)
    printf("Full Path: %s\n" full_path)
    
    ; Also show in a dialog box for user convenience
    axlUIConfirm(sprintf(nil "Current PCB File Name:\n\n%s\n\nDesign Type: %s\nFull Path: %s" 
                        design_name design_type full_path))
    
    ; Return the design name for further use
    return(design_name)
  )
)

; Alternative function that just returns the filename without display
procedure(get_current_pcb_name()
  prog((design_name)
    ; Check if a design is currently open
    unless(axlIsDesignOpen()
      return(nil)
    )
    
    ; Get and return the current design name
    design_name = axlCurrentDesign()
    return(design_name)
  )
)

; Function to get detailed design information
procedure(get_pcb_info()
  prog((info_table design_name design_type working_dir full_path)
    ; Check if a design is currently open
    unless(axlIsDesignOpen()
      return(nil)
    )
    
    ; Get design information
    design_name = axlCurrentDesign()
    design_type = axlDesignType(t)
    working_dir = axlGetVariable("WORKINGDIR")
    full_path = strcat(working_dir "/" design_name)
    
    ; Create a table with the information
    info_table = makeTable("pcb_info" nil)
    info_table["design_name"] = design_name
    info_table["design_type"] = design_type
    info_table["working_dir"] = working_dir
    info_table["full_path"] = full_path
    
    return(info_table)
  )
)

; Load message
println("Get PCB Filename Script loaded.")
println("Commands available:")
println("  get_pcb_filename    - Display current PCB file name with details")
println("  get_current_pcb_name - Return just the filename")
println("  get_pcb_info        - Return detailed info table")
