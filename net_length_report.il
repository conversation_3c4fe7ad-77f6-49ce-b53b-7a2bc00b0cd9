;; =========================================================================
;; Net Length Report Script for Allegro
;; Lists net lengths sorted by class/group and netname with electrical rules
;; =========================================================================

axlCmdRegister("report_net_lengths" `report_net_lengths)

procedure(report_net_lengths()
  prog((outfile nets net_classes net_groups net_data sorted_data)
    ; Create output file
    outfile = outfile("./net_length_report.txt" "w")
    
    ; Get all nets in the design
    nets = axlDBGetDesign()->nets
    
    ; Initialize data structures
    net_classes = makeTable("net_classes")
    net_groups = makeTable("net_groups")
    net_data = makeTable("net_data")
    
    ; Collect data for each net
    foreach(net nets
      net_name = net->name
      net_length = axlDBGetLength(net)
      
      ; Get net class if exists
      net_class = net->prop->NET_PHYSICAL_TYPE
      if(net_class == nil then
        net_class = "UNCLASSIFIED"
      )
      
      ; Get net group if exists
      net_group = net->prop->BUS_NAME
      if(net_group == nil then
        net_group = "UNGROUPED"
      )
      
      ; Get electrical rules if they exist
      elec_rules = ""
      if(net->prop->ELECTRICAL_CONSTRAINT_SET != nil then
        elec_rules = net->prop->ELECTRICAL_CONSTRAINT_SET
      )
      
      ; Store data
      net_classes[net_class] = t
      net_groups[net_group] = t
      
      ; Create key for sorting
      sort_key = sprintf(nil "%s:%s:%s", net_class, net_group, net_name)
      net_data[sort_key] = list(net_name, net_class, net_group, net_length, elec_rules)
    )
    
    ; Sort the data
    sorted_keys = sort(getkeys(net_data))
    sorted_data = mapcar(lambda(k) net_data[k], sorted_keys)
    
    ; Write header
    fprintf(outfile "NET LENGTH REPORT\n")
    fprintf(outfile "=====================================\n")
    fprintf(outfile "Generated: %s\n\n", getCurrentTime())
    
    ; Write report
    current_class = ""
    current_group = ""
    
    foreach(entry sorted_data
      net_name = nth(0, entry)
      net_class = nth(1, entry)
      net_group = nth(2, entry)
      net_length = nth(3, entry)
      elec_rules = nth(4, entry)
      
      ; Print class header if changed
      if(current_class != net_class then
        fprintf(outfile "\nCLASS: %s\n", net_class)
        fprintf(outfile "-------------------------------------\n")
        current_class = net_class
        current_group = ""
      )
      
      ; Print group header if changed
      if(current_group != net_group then
        fprintf(outfile "\n  GROUP: %s\n", net_group)
        fprintf(outfile "  -----------------------------------\n")
        current_group = net_group
      )
      
      ; Format length in mils and inches
      length_mils = net_length
      length_inches = net_length / 1000.0
      
      ; Print net info
      if(elec_rules == "" then
        fprintf(outfile "    %-30s  %.2f mils (%.3f inches)\n", 
                net_name, length_mils, length_inches)
      else
        fprintf(outfile "    %-30s  %.2f mils (%.3f inches)  [RULE: %s]\n", 
                net_name, length_mils, length_inches, elec_rules)
      )
    )
    
    ; Write summary
    fprintf(outfile "\n\nSUMMARY\n")
    fprintf(outfile "=====================================\n")
    fprintf(outfile "Total Nets: %d\n", length(nets))
    fprintf(outfile "Net Classes: %d\n", length(getkeys(net_classes)))
    fprintf(outfile "Net Groups: %d\n", length(getkeys(net_groups)))
    
    ; Close file
    close(outfile)
    
    ; Inform user
    axlUIConfirm(sprintf(nil "Net length report generated: %s/net_length_report.txt", axlGetVariable("WORKINGDIR")))
    
    return(t)
  )
)

procedure(getCurrentTime()
  prog((time_str)
    time_str = sprintf(nil "%s", getCurrentTime())
    return(time_str)
  )
)

; Fix for getCurrentTime function
procedure(getCurrentTime()
  prog((now)
    now = parseTime(getCurrentTimeStamp())
    return(sprintf(nil "%d-%02d-%02d %02d:%02d:%02d", 
           now->year, now->month, now->day, 
           now->hour, now->minute, now->second))
  )
)

; Load message
println("Net Length Report Script loaded. Run 'report_net_lengths' to generate report.")