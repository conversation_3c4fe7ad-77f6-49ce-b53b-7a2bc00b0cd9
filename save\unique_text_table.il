;; =========================================================================
;; Unique Text Table Script for Cadence Allegro
;; - Creates a list of 100 text items with 40 duplicates
;; - Creates a table with unique entries from the list
;; - Demonstrates filtering duplicates using table keys
;; =========================================================================

axlCmdRegister("create_unique_text_table" 'create_unique_text_table)

procedure(create_unique_text_table()
  prog((text_items unique_table duplicate_count original_count unique_count)
    ; Create a list to hold the text items
    text_items = nil
    
    ; Generate 60 unique text items
    for(i 1 60
      text_item = sprintf(nil "Item-%d" i)
      text_items = cons(text_item text_items)
    )
    
    ; Generate 40 duplicate items (randomly selected from the first 20)
    for(i 1 40
      ; Select a random item from the first 20 items (1-20)
      random_index = 1 + random(20)
      text_item = sprintf(nil "Item-%d" random_index)
      text_items = cons(text_item text_items)
    )
    
    ; Shuffle the list to mix duplicates throughout
    text_items = shuffle_list(text_items)
    
    ; Count original items
    original_count = length(text_items)
    
    ; Display header
    println("Creating Table with Unique Entries:")
    println("==================================")
    
    ; Create a table to store unique text items
    unique_table = makeTable("unique_items" nil)
    
    ; Add each text item to the table, but only if it doesn't already exist
    duplicate_count = 0
    foreach(item text_items
      ; Check if the item already exists in the table
      if(unique_table[item] then
        ; Item already exists, count as duplicate
        duplicate_count = duplicate_count + 1
        printf("Duplicate found: %s (skipping)\n" item)
      else
        ; Item doesn't exist, add it to the table
        unique_table[item] = t
        printf("Adding to table: %s\n" item)
      )
    )
    
    ; Get the count of unique items
    unique_count = length(getkeys(unique_table))
    
    ; Display summary
    println("\nSummary:")
    println("========")
    printf("Original list: %d items\n" original_count)
    printf("Unique items: %d\n" unique_count)
    printf("Duplicates found: %d\n" duplicate_count)
    
    ; Display the unique items
    println("\nUnique Items in Table:")
    println("=====================")
    
    ; Get all keys (unique items) and sort them
    unique_items = sort(getkeys(unique_table))
    
    ; Display each unique item
    foreach(item unique_items
      printf("  %s\n" item)
    )
    
    ; Return the table for further use
    return(unique_table)
  )
)

; Helper function to shuffle a list
procedure(shuffle_list(lst)
  prog((result len idx)
    result = nil
    len = length(lst)
    
    ; Copy the list
    temp_list = append(nil lst)
    
    ; Shuffle using Fisher-Yates algorithm
    while(temp_list
      ; Pick a random element from the remaining list
      idx = 1 + random(length(temp_list))
      
      ; Add it to the result
      result = cons(nthelem(idx temp_list) result)
      
      ; Remove it from the temp list
      temp_list = append(firstn(idx-1 temp_list) nthelem(idx+1 temp_list))
    )
    
    return(result)
  )
)

; Load message
println("Unique Text Table Script loaded. Run 'create_unique_text_table' to use.")