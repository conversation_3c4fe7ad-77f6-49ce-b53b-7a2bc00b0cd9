;; =========================================================================
;; Unique Values Table Script for Cadence Allegro
;; - Creates a list of variables with some duplicate values
;; - Saves only unique values to a table
;; - Displays the unique values
;; =========================================================================

axlCmdRegister("create_unique_values_table" 'create_unique_values_table)

procedure(create_unique_values_table()
  prog((values unique_table unique_values)
    ; Create a list with duplicate values
    values = list("b" "d" "b" "a" "c" "c")
    
    ; Display the original list
    println("Original list of values:")
    foreach(value values
      printf("  %s\n" value)
    )
    
    ; Create a table to store unique values
    unique_table = makeTable("unique_values" nil)
    
    ; Add each value to the table, using the value as both key and value
    ; This automatically eliminates duplicates since table keys are unique
    foreach(value values
      unique_table[value] = value
    )
    
    ; Get the unique values (keys of the table)
    unique_values = getkeys(unique_table)
    
    ; Display the unique values
    println("\nUnique values stored in table:")
    foreach(value unique_values
      printf("  %s\n" value)
    )
    
    ; Display the table contents
    println("\nTable contents:")
    foreach(key getkeys(unique_table)
      printf("  Key: %s, Value: %s\n" key unique_table[key])
    )
    
    ; Return the table for further use
    return(unique_table)
  )
)

; Load message
println("Unique Values Table Script loaded. Run 'create_unique_values_table' to use.")