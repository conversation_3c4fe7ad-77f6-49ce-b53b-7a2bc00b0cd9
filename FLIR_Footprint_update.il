;; =========================================================================
;; FLIR Footprint Update Script for Cadence Allegro
;; - Extracts all symbols from current PCB into "original_library" directory
;; - Creates directory if it doesn't exist
;; - Handles duplicate symbols
;; - Provides progress feedback during operation
;;
;; - We keep track of the PCB name and footprint name so we know which PCB file we 
;; - saved the footprint from. Either the variable Unique_Footprints is setto nil or 
;; - reads the csv file
;; - 
;; -  The format for the file name is :
;; -  1 = PCB FileName 
;; -  2 = footprint_name 
;; -  3 = Place_Bound_Shape - does the shape exist
;; -  4 = Max_Height - value or nil if it does not exist
;; -  5 = DFA_Bound_Shape - does the shape exist
;; -  6 = DISPLAY_TOP - did we create the shape on the display top layer. 
;; =========================================================================
;
; load "e:/pads/allegro/skill/ai/FLIR_Footprint_update.il"
;
;
; Explain how to use the structure, read it from a file or initialize it, add items and
; save it to a file.

; 1. First define the structure  - Create_File_structure()
; 2. Second create a component(this contains a pointer to a structure template) can use ->
;     to access the fields in a component - create_pcb_component(footprint_name pcb_name
;     place_bound max_height dfa_bound display_top)
; 3. Make a table so we can create and/or get components - create_component_collection() returns pointer
;     to a data structure that contains the structure (component) 
; 4. We can display the structure filed names and data - display_component(component)
; 5. We can update the fields in the structure  - update_component_field(component
;    Footprint_Key field_name new_value)
; 6. We can get the value of a field - get_component_field(component field_name)
;
; We not have a data structure called component and functions to write or read any fileds in the structure
; now we need to create a table to hold multiple structures of component
;
; 7. We now need to create a table that contains components and count to start -
;    create_component_collection() returns collection
; 8. Its time to read the CSV file if it exists.  load_collection_from_csv(filename) nil if no file
; 9. Ok. we have a table called collection to store data. 
; 10.Read and save footprints and update table. Go thru all footprints and then write the csv file. 
; 
; List functions to work with collection
;
; add_component_to_collection(collection component)
;
; get_collection_size(collection)
; 
; get_component_by_index(collection index)
;
; find_components_by_footprint(collection footprint_name)
;
; update_component_field(component field_name new_value)
;
; get_component_field(component field_name)
;
; 11.  Save the table to csv file



;********************************************************************
;
;	Structure routines
;
;********************************************************************

; Create_File_structure - creates a structure called PCB Component with the 7 items above. This is a template
; with the 7 values so you can adcess them by name.
;
; When you define a structure SKILL creates the following functions
;
; make_pcb_component() - Constructor (creates new instance) - empty component. .
;
; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
;
; Field accessors - Access fields using arrow notation (->)


; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
; Field accessors - Access fields using arrow notation (->)


; create_pcb_component(Footprint_name pcb_name place_bound max_height dfa_bound display_top
; - creates a variable and returns that variable called component(its local). It also calls
;
; make_pcb_component() is created by skill (see above).
;

;
; display_component(component) - displays the passed structure components 7 items
;



; update_component_field(component field_name new_value) - updates the provided field name with the new value
;  in teh passed component structure

;******************************************************
;
; PCB Component Structure Definition
; Only define if not already defined to prevent redefinition warnings
;
;******************************************************
; Define the PCB component structure
; Note: SKILL will warn about redefinition but this is harmless
defstruct(pcb_component
  pcb_name footprint_name place_bound_shape max_height dfa_bound_shape display_top
)
printf("PCB component structure defined\n")
;

 ;
; get_component_field(component field_name) gets the current field_name valur from the current component.
;  returns nil if no value to pass. 
;;
;; definitions for me
;;

;; 1  - structure   -  creates a group of the same variables so they can be used later.  
;; 2  - component   -  pointer to a structure
;; 3  - collection  - 

;;Think of it as:

;;collection = Base pointer to a control block
;;collection["components"] = Pointer to linked list of components
;;collection["count"] = Counter (like your COMPONENT_COUNT)

;; 4  - constructor - 
;; 5  - table -     - used to store multiple components


; =========================================================================
; STRUCTURE COLLECTION MANAGEMENT
; =========================================================================
;
;create_component_collection() - Makes a table called component_collection
; and sets it to collection
; set it components ??? and no count. 

;
;add_component_to_collection(collection component) - set collection to table and component 
; to values
;
;
; get_collection_size(collection) - gets the size of the passed collection
; 
;
; find_components_by_footprint(collection footprint_name)
;   

; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================
;
; save_collection_to_csv(collection filename)
;
; Routine to save the collection by adding a header and then each entry in a CSV file.
;

;
; display_collection_summary(collection)


;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================
;
; load_collection_from_csv(filename) - reads csv file into collection ansd returns the 
;  new variable
;
;
; import_csv_to_collection(existing_collection filename) - import and merge existing collection 
;  into csv file.
;
; parse_csv_line(line) - 

; 
; trim_string(str)
;
;
; create_sample_csv(filename)

;
; make_pcb_component
; 

axlCmdRegister("flir_footprint_update" 'flir_footprint_update)

;******************************************************
;
; Main Routine
;
;******************************************************


procedure(flir_footprint_update()

  prog((original_dir target_dir footprint_names unique_symbols total_count success_count duplicate_count collection)
 

     duplicate_count = 0
     footprint_counter = 0

;
; Create a structure to use with the excel file and the footprints.
;

Create_File_structure()						
 
;
; Check if a design is currently open if not exit program
;
    current_design = axlDBGetDesign()
    unless(current_design
        axlUIConfirm("No design is currently open.")
        println("No design is currently open.")
        return(nil)
    );End Unless

    ; Debug: Show design information
    printf("Current design: %s\n" axlCurrentDesign())
    printf("Design components count: %d\n" length(current_design->components))
    printf("Design symbols count: %d\n" length(current_design->symbols))

    ; Check if there are any components in the design
    unless(current_design->components
      axlUIConfirm("No components found in the current design")
      return(nil)
    )
  ;
  ; Check if we're in the PCB Editor(not in symbol editor)
  ;
 
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    ); End unless
    
    ;
    ; Set target directory for saving symbols
    ;

    original_dir = "./original_Library"
    target_dir = "./new_Library"
    
   ;
   ; Get PCB file name
   ;
   
   Pcb_File_Name = axlCurrentDesign()
   
   ;
   ; Get the design type
   ;
       design_type = axlDesignType(t)
      
   ;
   ; Get the working directory
   ;
       working_dir = getWorkingDir()
       
       ; Construct full path
    
    full_path = strcat(working_dir, "/" , Pcb_File_Name)
   
    
   ;
   ; If Directory for symbols does not exist create it and output message.
   ;

    ; Create directories silently
    createDir(original_dir)
    createDir(target_dir)
;     
; if Footprint_List_File exists then get file into a variable unique_footprints otherwise 
; create the structure for unique_fooptrints
; 

    ; Always start with fresh collection for this run
    ; (Previous CSV will be overwritten with updated data)
    collection = create_component_collection()
    printf("Debug: Collection created: %L\n" collection)
    printf("Debug: Collection components: %L\n" collection["components"])

    ; If CSV exists, we could load it here for incremental updates
    ; but for now we'll rebuild it fresh each time

 
 
    ;
    ; Get all footprint names in the design.
    ;

    footprint_names = axlDBGetDesign()->symbols~>name

    ; Debug: Show what footprint names were found
    printf("Footprint names found: %d\n" length(footprint_names))
    when(footprint_names
      if(length(footprint_names) > 5 then
        printf("First 5 footprints: %s %s %s %s %s ...\n"
               nth(0 footprint_names) nth(1 footprint_names) nth(2 footprint_names)
               nth(3 footprint_names) nth(4 footprint_names))
      else
        printf("All footprints: %L\n" footprint_names)
      )
    )
   
   
    
 foreach(current_footprint_name footprint_names

  ;
 ; Check if this entry already exists as a value in the table
 ;

   existing_components = find_components_by_footprint(collection current_footprint_name)
   if(length(existing_components) > 0 then
 ;
 ; Entry already exists as a value, count as duplicate. change
 ; Update pcb _name
 ;
	duplicate_count = duplicate_count + 1
        component = car(existing_components)
        update_component_field(component "pcb_name" Pcb_File_Name)

 ;
 ;	Add to collection
 ;

   else
;
; Entry doesn't exist, add it to the table
;
    component = create_pcb_component(current_footprint_name Pcb_File_Name nil nil nil nil)

    add_component_to_collection(collection component)

   );End If

 ); End Foreach
 
 ;
 ; Save collection to file
 ;

save_collection_to_csv(collection "Footprint_file_list.CSV")

display_collection_summary(collection duplicate_count length(footprint_names))


    
    
 ;  
 ;  
 ;
    
    printf("Starting symbol extraction phase...\n")

    ; PHASE 1: Extract ALL symbols while in board mode
    extraction_count = 0
    components = collection["components"]

    printf("PHASE 1: Extracting %d unique footprints to directories...\n" collection["count"])

    foreach(component components
      printf("Extracting footprint: %s\n" component->footprint_name)
      when(extract_symbol_only(component target_dir)
        extraction_count = extraction_count + 1
      )

      ; Update progress every 5 footprints
      when(remainder(extraction_count 5) == 0
        printf("Extraction progress: %d of %d footprints extracted\n" extraction_count collection["count"])
      )
    )

    printf("PHASE 1 Complete: %d of %d footprints extracted\n" extraction_count collection["count"])

    ;
    ; Ask user if they want to proceed with FLIR modifications
    ;
    proceed_with_flir = axlUIYesNo("All symbols have been extracted to directories.\n\nDo you want to proceed with DISPLAY_TOP_LAYER modifications?\n\nYes = Apply DISPLAY_TOP_LAYER updates to symbols\nNo = Skip DISPLAY_TOP_LAYER updates (extraction only)")

    if(proceed_with_flir then
      ;
      ; Get FLIR expansion value from user via form
      ; The form appears after symbols are saved
      ;
      flir_expansion_value = get_flir_expansion_value()
      unless(flir_expansion_value
        printf("DISPLAY_TOP_LAYER expansion selection cancelled\n")
        return(nil)
      )

      ; PHASE 2: Process each extracted symbol with FLIR updates
      success_count = 0
      printf("PHASE 2: Applying FLIR updates to %d extracted symbols with %fmm expansion...\n" extraction_count flir_expansion_value)

    foreach(component components
      printf("Processing FLIR updates for: %s\n" component->footprint_name)
      when(apply_flir_to_extracted_symbol(component target_dir flir_expansion_value)
        success_count = success_count + 1
      )

      ; Update progress every 5 footprints
      when(remainder(success_count 5) == 0
        printf("FLIR progress: %d of %d footprints processed\n" success_count extraction_count)
      )
    )

    printf("Completed: %d of %d unique footprints processed with FLIR updates\n" success_count collection["count"])

    else
      ; User chose to skip FLIR modifications
      printf("FLIR modifications skipped by user choice\n")
      printf("Symbols extracted to directories without modifications\n")
      success_count = extraction_count  ; Set success count to extraction count for reporting
    ) ; End if proceed_with_flir

    ; Create new CSV file with updated data
    create_updated_csv_file(collection)

    ; Report final results
    printf("Completed: %d of %d unique symbols extracted and updated with FLIR to %s\n"
           success_count collection["count"] target_dir)


  ); End Prog
); End Procedure

;******************************************************
;
; extract_symbol_only - Extract symbol to directories (Phase 1 - Board Mode)
;
;******************************************************

procedure(extract_symbol_only(component target_dir)
  prog((footprint_name symDef result symbol_path original_path)
    ;
    ; Get footprint name from our component structure
    ;
    footprint_name = component->footprint_name

    ; Skip if no footprint name
    unless(footprint_name
      return(nil)
    ); End unless

    ; Show progress
    printf("Extracting footprint: %s to original and new directories\n" footprint_name)

    ; Find the symbol definition for this footprint name
    ; Search through all components in design to find one with this footprint
    symDef = nil

    foreach(comp axlDBGetDesign()->components
      when(equal(comp->symbol->name footprint_name)
        symDef = comp->symbol->definition
      )
    )

    ; Check if we found the symbol definition
    unless(symDef
      printf("ERROR: Could not find symbol definition for footprint: %s\n" footprint_name)
      return(nil)
    )

    ; Check if symbol already exists to avoid overwrite prompts
    original_path = strcat("./original_Library\\" footprint_name ".dra")
    target_path = strcat(target_dir "\\" footprint_name ".dra")

    ; Extract to original directory to preserve original
    if(isFile(original_path) then
      result = t  ; Skip if already exists
    else
      result = axlWritePackageFile(symDef "./original_Library")
      unless(result
        printf("  ERROR: Failed to extract original: %s\n" footprint_name)
        return(nil)
      )
    )

    ; Also extract to target directory for FLIR processing
    unless(isFile(target_path)
      result = axlWritePackageFile(symDef target_dir)
      unless(result
        printf("  ERROR: Failed to extract FLIR copy: %s\n" footprint_name)
        return(nil)
      )
    )

    ; Return result
    return(result)
  ) ; End Prog
) ; End procedure

;******************************************************
;
; apply_flir_to_extracted_symbol - Apply FLIR updates to extracted symbol (Phase 2)
;
;******************************************************

procedure(apply_flir_to_extracted_symbol(component target_dir expansion_value)
  prog((footprint_name symbol_path)
    ;
    ; Get footprint name from our component structure
    ;
    footprint_name = component->footprint_name

    ; Skip if no footprint name
    unless(footprint_name
      return(nil)
    ); End unless

    ; Construct path to extracted symbol file
    symbol_path = strcat(target_dir "\\" footprint_name ".dra")

    ; Check if the file exists
    unless(isFile(symbol_path)
      printf("ERROR: Symbol file not found: %s\n" symbol_path)
      return(nil)
    )

    ; Apply FLIR updates to the extracted symbol
    when(apply_flir_updates_to_symbol(symbol_path footprint_name expansion_value)
      printf("  OK FLIR updates applied: %s\n" footprint_name)
      ; Update component to indicate DISPLAY_TOP was created
      update_component_field(component "display_top" "FLIR_Updated")
      return(t)
    )

    ; If we get here, the update failed
    return(nil)
  ) ; End Prog
) ; End procedure

;******************************************************
;
; apply_flir_updates_to_symbol - Load symbol and apply FLIR updates
;
;******************************************************

procedure(apply_flir_updates_to_symbol(symbol_path footprint_name expansion_value)
  prog((current_design_name)
    ; Save current design name to return to it later
    current_design_name = axlCurrentDesign()

    printf("  Loading symbol for FLIR update: %s\n" footprint_name)
    printf("  Debug: Attempting to open symbol at: %s\n" symbol_path)

    ; Try to open the symbol file directly without closing first
    shell_command = sprintf(nil "open %s" symbol_path)
    printf("  Debug: Shell command: %s\n" shell_command)

    ; Try the shell command and capture any output
    shell_result = axlShell(shell_command)
    printf("  Debug: Shell command result: %L\n" shell_result)

    unless(shell_result
      printf("  ERROR: Failed to load symbol with command: %s\n" shell_command)

      ; Try alternative: open directly
      printf("  Debug: Trying direct open approach\n")
      unless(axlShell(shell_command)
        printf("  ERROR: All symbol loading methods failed for: %s\n" symbol_path)
        return(nil)
      )
    )

    ; Wait a moment for the symbol to load
    ; (In practice, you might need a more robust way to check if loading is complete)

    ; Check if we're now in Package Symbol Editor mode
    design_type = axlDesignType(t)
    printf("  Debug: Design type after loading: %s\n" design_type)

    unless(design_type == "PACKAGE"
      printf("  ERROR: Not in Package Symbol Editor mode after loading: %s (got: %s)\n" footprint_name design_type)
      return(nil)
    )

    ; Suppress save prompts during symbol modification
    axlSetVariable("SUPPRESS_SAVE_PROMPTS" "YES")

    ; Apply the FLIR updates (core logic from FLIR_SYMBOL_UPDATE.il)
    when(perform_flir_symbol_update(expansion_value component)
      printf("  ? FLIR symbol update completed: %s\n" footprint_name)

      ; Symbol is automatically saved by Allegro when design is modified
      printf("  ? Symbol updated: %s\n" footprint_name)

      ; Symbol processing complete

      return(t)
    )

    ; If we get here, the update failed
    printf("  ? FLIR symbol update failed: %s\n" footprint_name)
    return(nil)
  ); End Prog
);End

;******************************************************
;
; perform_flir_symbol_update - Core FLIR update logic
;
;******************************************************

procedure(perform_flir_symbol_update(expansion_value component)
  prog((shapes_to_delete shapes_to_copy New_Shape shape poly exp_poly new_shape
        place_bound_shapes place_bound_count max_height_value dfa_bound_shapes dfa_bound_count
        display_top_added)
    ; Use the expansion value passed from the form selection
    Increase_Value = expansion_value
    display_top_added = "no"  ; Default to no

    printf("    Applying FLIR updates with increase value: %f\n" Increase_Value)

    ; Collect PLACE_BOUND data
    place_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/PLACE_BOUND_TOP")
    place_bound_count = if(place_bound_shapes then length(place_bound_shapes) else 0)

    ; Get max height from PLACE_BOUND layer
    max_height_value = 0.0
    when(place_bound_shapes
      foreach(shape place_bound_shapes
        ; For PLACE_BOUND shapes, height is typically stored in the shape's extent or text
        height_found = nil

        ; Method 1: Check if shape has text with height value
        when(shape->textString
          ; Try to extract numeric value from text
          text_val = atof(shape->textString)
          when(text_val > 0
            height_found = text_val
          )
        )

        ; Method 2: Check shape extent/size properties
        unless(height_found
          when(shape->extent
            ; Shape extent might contain height information
            when(shape->extent->z || shape->extent->height
              height_found = if(shape->extent->z then shape->extent->z else shape->extent->height)
            )
          )
        )

        ; Method 3: Check bBox Z-dimension
        unless(height_found
          when(shape->bBox && length(shape->bBox) >= 2
            pt1 = car(shape->bBox)
            pt2 = cadr(shape->bBox)
            when(length(pt1) >= 3 && length(pt2) >= 3
              z1 = caddr(pt1)
              z2 = caddr(pt2)
              when(z1 && z2 && abs(z2 - z1) > 0
                height_found = abs(z2 - z1)
              )
            )
          )
        )

        ; Method 4: Check shape parameters for height
        unless(height_found
          when(shape->parameters
            foreach(param shape->parameters
              when(param && param->name
                case(param->name
                  ("HEIGHT" "height" "Height" "THICKNESS" "thickness"
                    when(param->value
                      height_found = atof(param->value)
                    )
                  )
                )
              )
            )
          )
        )

        ; Update max height if we found a valid height value
        when(height_found && height_found > max_height_value
          max_height_value = height_found
          printf("    Found height value: %f from PLACE_BOUND shape\n" height_found)
        )
      )
    )

    ; Collect DFA_BOUND data
    dfa_bound_shapes = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")
    dfa_bound_count = if(dfa_bound_shapes then length(dfa_bound_shapes) else 0)

    ; Delete all shapes on DISPLAY_TOP layer
    shapes_to_delete = axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP")
    if(shapes_to_delete then
      axlDeleteObject(shapes_to_delete)
      printf("    Deleted existing shapes on DISPLAY_TOP layer\n")
    else
      printf("    No existing shapes on DISPLAY_TOP layer\n")
    );End If

    ; Get shape on DFA_BOUND layer
    shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/DFA_BOUND_TOP")

    ; Check if shapes exist
    if(length(shapes_to_copy) == 0 then
      printf("    WARNING: No shape found on DFA_BOUND_TOP layer - trying PLACE_BOUND_TOP\n")
      ; Try PLACE_BOUND_TOP as fallback
      shapes_to_copy = axlDBGetShapes("PACKAGE GEOMETRY/PLACE_BOUND_TOP")
      if(length(shapes_to_copy) == 0 then
        printf("    WARNING: No shapes found on PLACE_BOUND_TOP either - continuing without FLIR update\n")
        display_top_added = "no_shapes_found"
        ; Still update component data even if no shapes to copy
        when(component
          update_component_field(component "place_bound_shape" place_bound_count)
          update_component_field(component "max_height" max_height_value)
          update_component_field(component "dfa_bound_shape" dfa_bound_count)
          update_component_field(component "display_top" display_top_added)
        )
        ; Symbol is automatically saved by Allegro when loaded
        printf("    OK Symbol processed without FLIR updates: %s\n" component->footprint_name)
        return(t)
      )
    );End If

    if(length(shapes_to_copy) >= 2 then
      printf("    WARNING: Multiple shapes found on DFA_BOUND_TOP layer, using first one\n")
    );End If

    ; Copy shape from DFA_BOUND_LAYER
    New_Shape = axlCopyObject(shapes_to_copy ?angle 0)

    ; Check if copy succeeded
    unless(New_Shape
      printf("    ERROR: Failed to copy shape from DFA_BOUND_TOP layer\n")
      return(nil)
    ); End unless

    ; Change Layer to display top
    New_Shape = axlChangeLayer(New_Shape "PACKAGE GEOMETRY/DISPLAY_TOP")

    unless(New_Shape
      printf("    ERROR: Failed to change shape to DISPLAY_TOP layer\n")
      return(nil)
    ); End unless

    ; Get the shape on Display Top Layer for expansion
    shape = car(axlDBGetShapes("PACKAGE GEOMETRY/DISPLAY_TOP"))

    ; Convert shape to polygon
    poly = axlPolyFromDB(shape)

    ; Expand polygon by Increase_Value
    exp_poly = car(axlPolyExpand(poly Increase_Value 'None))

    ; Delete the original shape before creating the expanded one
    axlDeleteObject(shape)

    ; Create new expanded shape
    new_shape = axlDBCreateShape(exp_poly t "PACKAGE GEOMETRY/DISPLAY_TOP")

    if(new_shape then
      printf("    ? Created expanded DISPLAY_TOP shape\n")
      display_top_added = "yes"
    else
      printf("    ? Failed to create expanded DISPLAY_TOP shape\n")
    )

    ; Update component with collected data
    when(component
      update_component_field(component "place_bound_shape" place_bound_count)
      update_component_field(component "max_height" max_height_value)
      update_component_field(component "dfa_bound_shape" dfa_bound_count)
      update_component_field(component "display_top" display_top_added)
      printf("    Updated component data: PLACE_BOUND=%d, MAX_HEIGHT=%f, DFA_BOUND=%d, DISPLAY_TOP=%s\n"
             place_bound_count max_height_value dfa_bound_count display_top_added)
    )

    ; Restore save prompts before returning
    axlSetVariable("SUPPRESS_SAVE_PROMPTS" "NO")

    ; Return success status
    return(equal(display_top_added "yes"))
  ); End Prog
); End procedure

;******************************************************
;
; get_flir_expansion_value - Show form to get user's expansion preference
;
;******************************************************

procedure(get_flir_expansion_value()
  prog((form_file myform result)
    ; Create the form file
    create_flir_expansion_form()

    ; Set up form variables
    form_file = "./flir_expansion_form.form"

    ; Initialize global variables for form communication
    flir_expansion_selected = 0.125  ; Default to "Least"
    flir_form_completed = nil

    ; Create and display the form
    myform = axlFormCreate((gensym) form_file nil 'flir_form_action t)
    unless(myform
      printf("ERROR: Failed to create FLIR expansion form\n")
      return(nil)
    )

    ; Set default selection to "Least"
    axlFormSetField(myform "Least-.125MM" t)

    ; Display the form - this will be modal and block until closed
    result = axlFormDisplay(myform)

    ; The form action handler will set flir_expansion_selected
    ; Return the selected value (or nil if cancelled)
    return(flir_expansion_selected)
  ); End Prog
); End procedure

;******************************************************
;
; flir_form_action - Handle form actions
;
;******************************************************

procedure(flir_form_action(myform)
  prog((field_name)
    field_name = myform->curField

    case(field_name
      ("Least-.125MM"
        printf("Selected: Least expansion (0.125mm)\n")
        flir_expansion_selected = 0.125
      )
      ("Nominal-.25MM"
        printf("Selected: Nominal expansion (0.25mm)\n")
        flir_expansion_selected = 0.25
      )
      ("Most-.5MM"
        printf("Selected: Most expansion (0.5mm)\n")
        flir_expansion_selected = 0.5
      )
      ("OK"
        printf("FLIR expansion confirmed: %fmm - Starting footprint processing...\n" flir_expansion_selected)
        flir_form_completed = t
        axlFormClose(myform)
      )
      ("Cancel"
        printf("FLIR expansion selection cancelled\n")
        flir_expansion_selected = nil
        flir_form_completed = t
        axlFormClose(myform)
      )
    ); End case
  ); End Prog
); End procedure

;******************************************************
;
; create_flir_expansion_form - Create BNF form file for expansion selection
;
;******************************************************

procedure(create_flir_expansion_form()
  prog((form_file myform)
    form_file = "./flir_expansion_form.form"
    myform = outfile(form_file "w")

    fprintf(myform "FILE_TYPE=FORM_DEFN VERSION=2\n")
    fprintf(myform "FORM AUTOGREYTEXT\n")
    fprintf(myform "FIXED\n")
    fprintf(myform "PORT 35 20\n")
    fprintf(myform "HEADER \"FLIR FOOTPRINT UPDATE\"\n\n")
    fprintf(myform "TILE\n\n")

    fprintf(myform "## Title Text ## \n")
    fprintf(myform "TEXT \"Select DISPLAY_TOP Expansion Amount:\"\n")
    fprintf(myform "TLOC 1 1\n")
    fprintf(myform "ENDTEXT\n\n")

    fprintf(myform "## Instruction Text ## \n")
    fprintf(myform "TEXT \"Click OK to start processing with selected value\"\n")
    fprintf(myform "TLOC 1 2\n")
    fprintf(myform "ENDTEXT\n\n")

    fprintf(myform "## Group Definition ## \n")
    fprintf(myform "GROUP \"EXPANSION\"\n")
    fprintf(myform "GLOC 1 4\n")
    fprintf(myform "FSIZE 25 12\n")
    fprintf(myform "ENDGROUP\n\n")

    fprintf(myform "## Radio Button - Least ## \n")
    fprintf(myform "FIELD Least-.125MM\n")
    fprintf(myform "FLOC 2 6\n")
    fprintf(myform "CHECKLIST \"Least (0.125mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Radio Button - Nominal ## \n")
    fprintf(myform "FIELD Nominal-.25MM\n")
    fprintf(myform "FLOC 2 8\n")
    fprintf(myform "CHECKLIST \"Nominal (0.25mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Radio Button - Most ## \n")
    fprintf(myform "FIELD Most-.5MM\n")
    fprintf(myform "FLOC 2 10\n")
    fprintf(myform "CHECKLIST \"Most (0.5mm)\" \"rg\"\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## OK Button ## \n")
    fprintf(myform "FIELD OK\n")
    fprintf(myform "FLOC 5 13\n")
    fprintf(myform "MENUBUTTON \"Start Processing\" 12 2\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "## Cancel Button ## \n")
    fprintf(myform "FIELD Cancel\n")
    fprintf(myform "FLOC 19 13\n")
    fprintf(myform "MENUBUTTON \"Cancel\" 8 2\n")
    fprintf(myform "FGROUP \"EXPANSION\"\n")
    fprintf(myform "ENDFIELD\n\n")

    fprintf(myform "ENDTILE\n")
    fprintf(myform "ENDFORM\n")

    close(myform)
    printf("FLIR expansion form created: %s\n" form_file)
  ); End Prog
); End procedure


;;; =========================================================================
;;; STRUCTURE DEFINITION
;;; =========================================================================

;******************************************************
;
; This is an initial create the initial structure for each entry
;
;
;******************************************************
procedure(Create_File_structure()
;
; Structure is already defined at the top of the file
; This function is kept for compatibility but no longer needed
;
  printf("PCB component structure already defined\n")

); End Procedure

;******************************************************
;
; Function to create a new PCB component record
;
;******************************************************
procedure(create_pcb_component(footprint_name pcb_name place_bound max_height dfa_bound display_top)
  prog((component)
    ;
    ; Create new component structure instance
    ;
    component = make_pcb_component()

    ;
    ; Set all fields
    ;
    
    component->footprint_name = footprint_name
    component->pcb_name = pcb_name
    component->place_bound_shape = place_bound
    component->max_height = max_height
    component->dfa_bound_shape = dfa_bound
    component->display_top = display_top

    ;
    ; Return the created component
    ;
    return(component)
  )
)
;******************************************************
;
; Function to display component information
;
;******************************************************
procedure(display_component(component)
  prog(()
    printf("PCB Component Information:\n")
    printf("=========================\n")
     printf("Footprint Name:     %s\n" component->footprint_name)
   printf("PCB Name:           %s\n" component->pcb_name)
    printf("Place Bound Shape:  %s\n" component->place_bound_shape)
    printf("Max Height:         %s\n" component->max_height)
    printf("DFA Bound Shape:    %s\n" component->dfa_bound_shape)
    printf("Display Top:        %s\n" component->display_top)
    printf("\n")
  )
)

;******************************************************
;
; Function to update component fields
;
;******************************************************
procedure(update_component_field(component field_name new_value)
  prog(()
    case(field_name
      ("pcb_name"
        component->pcb_name = new_value)
      ("footprint_name"
        component->footprint_name = new_value)
      ("place_bound_shape"
        component->place_bound_shape = new_value)
      ("max_height"
        component->max_height = new_value)
      ("dfa_bound_shape"
        component->dfa_bound_shape = new_value)
      ("display_top"
        component->display_top = new_value)
      (t
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    ;printf("Updated %s to: %s\n" field_name new_value)
    return(t)
  )
)
;******************************************************
;
; Function to get component field value
;
;******************************************************
procedure(get_component_field(component field_name)
  prog((value)
    case(field_name
      ("pcb_name" 
        value = component->pcb_name)
      ("footprint_name" 
        value = component->footprint_name)
      ("place_bound_shape" 
        value = component->place_bound_shape)
      ("max_height" 
        value = component->max_height)
      ("dfa_bound_shape" 
        value = component->dfa_bound_shape)
      ("display_top" 
        value = component->display_top)
      (t 
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    return(value)
  )
)

;;; =========================================================================
;;; STRUCTURE COLLECTION MANAGEMENT
;;; =========================================================================

;
; Function to create a collection of components (using a table)
;
procedure(create_component_collection()
  prog((collection)
    ;
    ; Create table to hold multiple components
    ;
    collection = makeTable("component_collection" nil)
    collection["components"] = nil
    collection["count"] = 0

    ;
    ; Set structure field definitions for self-describing collection
    ;
    collection["structure_name"] = "pcb_component"
    collection["field_names"] = list(
      "pcb_name"
      "footprint_name"
      "place_bound_shape"
      "max_height"
      "dfa_bound_shape"
      "display_top"
    )
    collection["field_count"] = length(collection["field_names"])
    collection["csv_headers"] = list(
      "PCB Name"
      "Footprint Name"
      "Place Bound Shape"
      "Max Height"
      "DFA Bound Shape"
      "Display Top"
    )
    collection["version"] = "1.0"
    collection["created_date"] = getCurrentTime()

    return(collection)
  )
)

;
; Function to add component to collection
;
procedure(add_component_to_collection(collection component)
  prog((components)
    ;
    ; Get current components list
    ;
    components = collection["components"]

    ;
    ; Add new component
    ;
    if(components then
      collection["components"] = append(components list(component))
    else
      collection["components"] = list(component)
    )

    ;
    ; Update count
    ;
    collection["count"] = collection["count"] + 1

    printf("Added component to collection. Total count: %d\n" collection["count"])
    return(t)
  )
)

;
; Function to get collection size
;
procedure(get_collection_size(collection)
  return(collection["count"])
)

;
; Function to get component from collection by index
;
procedure(get_component_by_index(collection index)
  prog((components)
    components = collection["components"]

    ;
    ; Check bounds
    ;
    if(index < 1 || index > length(components) then
      printf("Index %d out of bounds (1 to %d)\n" index length(components))
      return(nil)
    )

    return(nthelem(index components))
  )
)

;
; Function to find components by footprint name
;
procedure(find_components_by_footprint(collection footprint_name)
  prog((components found_components)
    components = collection["components"]
    found_components = nil
    
    foreach(comp components

        when(equal(comp->footprint_name footprint_name)
        found_components = cons(comp found_components)
      )
    )
 
    return(reverse(found_components))
  )
)
; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================

;******************************************************
;
; Function to save component collection to CSV file
;
;******************************************************

procedure(save_collection_to_csv(collection filename)
  prog((file_handle components)
    ;
    ; Open file for writing
    ;
    file_handle = outfile(filename "w")
    unless(file_handle
      printf("Failed to create CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Write CSV header with quotes - match structure field order
    ;
    fprintf(file_handle "\"Footprint_Name\",\"PCB_Name\",\"Place_Bound_Shape\",\"Max_Height\",\"DFA_Bound_Shape\",\"Display_Top\"\n")

    ;
    ; Get components
    ;
    components = collection["components"]

    ;
    ; Write each component as CSV row with quotes around each field - match header order
    ;
    foreach(comp components
      fprintf(file_handle "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
        (if comp->footprint_name then comp->footprint_name else "")
        (if comp->pcb_name then comp->pcb_name else "")
        (if comp->place_bound_shape then comp->place_bound_shape else "")
        (if comp->max_height then comp->max_height else "")
        (if comp->dfa_bound_shape then comp->dfa_bound_shape else "")
        (if comp->display_top then comp->display_top else "")
      )
    )

    ;
    ; Close file
    ;
    close(file_handle)

    printf("Saved %d components to CSV file: %s\n" collection["count"] filename)
    return(t)
  )
)



;******************************************************

;
; Function to display collection summary
;
;******************************************************
procedure(display_collection_summary(collection duplicate_count total_footprints)
  prog((components unique_pcbs unique_footprints)

    components = collection["components"]
    unique_pcbs = nil
    unique_footprints = nil

    printf("Component Collection Summary:\n")
    printf("============================\n")
    printf("Unique Components in Collection: %d\n" collection["count"])

    ;
    ; Count unique PCBs and footprints
    ;
    foreach(comp components
      unless(member(comp->pcb_name unique_pcbs)
        unique_pcbs = cons(comp->pcb_name unique_pcbs)
      )
      unless(member(comp->footprint_name unique_footprints)
        unique_footprints = cons(comp->footprint_name unique_footprints)
      )
    )

    printf("Unique PCBs: %d\n" length(unique_pcbs))
    printf("Unique Footprints: %d\n" length(unique_footprints))
    printf("Total Footprints Processed: %d\n" total_footprints)
    printf("Duplicate Instances Found: %d\n" duplicate_count)
    printf("\n")
  )
)

;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================

;
; Enhanced function to load components from CSV file with proper CSV parsing
;
procedure(load_collection_from_csv(filename)
  prog((file_handle line collection fields comp row_count header_line)
    ;
    ; Check if file exists
    ;
    unless(isFile(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )

    ;
    ; Open file
    ;
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Create collection
    ;
    collection = create_component_collection()
    row_count = 0

    printf("Reading CSV file: %s\n" filename)

    ;
    ; Read header line
    ;
    if(gets(header_line file_handle) then
      printf("Header: %s\n" trim_string(header_line))
    )

    ;
    ; Read data lines
    ;
    while(gets(line file_handle)
      row_count = row_count + 1
      line = trim_string(line)

      ;
      ; Skip empty lines
      ;
      unless(strlen(line) == 0
        ;
        ; Parse CSV line with proper handling
        ;
        fields = parse_csv_line(line)



        ;
        ; Validate we have enough fields
        ;
        if(length(fields) >= 6 then
          ;
          ; Extract fields safely
          ;
          pcb_name = if(length(fields) >= 1 then trim_string(car(fields)) else "")
          footprint_name = if(length(fields) >= 2 then trim_string(cadr(fields)) else "")
          place_bound = if(length(fields) >= 3 then trim_string(caddr(fields)) else "")
          max_height = if(length(fields) >= 4 then trim_string(cadddr(fields)) else "")
          dfa_bound = if(length(fields) >= 5 then trim_string(nth(4 fields)) else "")
          display_top = if(length(fields) >= 6 then trim_string(nth(5 fields)) else "")

          ;
          ; Create component from CSV data
          ;
          comp = create_pcb_component(
            pcb_name
            footprint_name
            place_bound
            max_height
            dfa_bound
            display_top
          )
          add_component_to_collection(collection comp)
        else
          ; Skip row with insufficient fields
        )
      )
    )

    close(file_handle)
    printf("CSV import completed: %d components loaded from %d rows\n"
           collection["count"] row_count)

    ;
    ; CSV parsing complete
    ;

    return(collection)
  )
)

;
; Function to import and merge CSV data into existing collection
;
procedure(import_csv_to_collection(existing_collection filename)
  prog((imported_collection imported_components)
    ;
    ; Load CSV data
    ;
    imported_collection = load_collection_from_csv(filename)

    unless(imported_collection
      printf("Failed to import CSV data\n")
      return(nil)
    )

    ;
    ; Get imported components
    ;
    imported_components = imported_collection["components"]

    ;
    ; Add each imported component to existing collection
    ;
    foreach(comp imported_components
      add_component_to_collection(existing_collection comp)
    )

    printf("Merged %d components from CSV into existing collection\n"
           imported_collection["count"])
    return(existing_collection)
  )
)

;
; Function to parse CSV line with proper quote handling
;
procedure(parse_csv_line(line)
  prog((fields temp_line)
    ;
    ; Handle empty line
    ;
    if(strlen(line) == 0 then
      return(list("" "" "" "" "" ""))
    )

    ;
    ; Simple approach: Remove all quotes first, then split on comma
    ; This works for the quoted CSV format where each field is "field"
    ;
    temp_line = line

    ; Remove all quote characters
    temp_line = buildString(parseString(temp_line "\"") "")

    ; Split on comma
    fields = parseString(temp_line ",")

    ;
    ; Ensure we have exactly 6 fields (pad with empty strings if needed)
    ;
    while(length(fields) < 6
      fields = append(fields list(""))
    )

    ; Truncate if too many fields
    if(length(fields) > 6 then
      fields = list(nth(0 fields) nth(1 fields) nth(2 fields)
                   nth(3 fields) nth(4 fields) nth(5 fields))
    )

    return(fields)
  )
)

;
; Function to convert string to number (returns nil if not a valid number)
;
procedure(convert_to_number(str)
  prog((trimmed_str result)
    ; First trim the string
    trimmed_str = trim_string(str)

    ; Check if empty or nil
    unless(trimmed_str
      return(nil)
    )

    ; Check if string is empty after trimming
    if(strlen(trimmed_str) == 0 then
      return(nil)
    )

    ; Try to convert to number using atof (handles both integers and floats)
    result = atof(trimmed_str)

    ; atof returns 0.0 for invalid strings, so check if it's actually zero
    ; or if the original string started with "0"
    if(result == 0.0 then
      if(or(equal(substring(trimmed_str 1 1) "0")
            equal(trimmed_str "0")
            equal(trimmed_str "0.0")) then
        return(0.0)
      else
        return(nil)  ; Invalid number
      )
    else
      return(result)
    )
  )
)

;
; Function to trim whitespace from string
;
procedure(trim_string(str)
  prog((start_pos end_pos result)
    unless(str
      return("")
    )

    ;
    ; Find first non-space character
    ;
    start_pos = 1
    while(start_pos <= strlen(str) &&
          member(substring(str start_pos start_pos) list(" " "\t" "\n" "\r"))
      start_pos = start_pos + 1
    )

    ;
    ; Find last non-space character
    ;
    end_pos = strlen(str)
    while(end_pos >= 1 &&
          member(substring(str end_pos end_pos) list(" " "\t" "\n" "\r"))
      end_pos = end_pos - 1
    )

    ;
    ; Extract trimmed string
    ;
    if(start_pos <= end_pos then
      result = substring(str start_pos end_pos)
    else
      result = ""
    )

    return(result)
  )
)




;******************************************************
;
; create_updated_csv_file - Create CSV with updated component data
;
;******************************************************

procedure(create_updated_csv_file(collection)
  prog((csv_file components component csv_filename)
    csv_filename = "FLIR_Updated_Components.csv"

    printf("Creating updated CSV file: %s\n" csv_filename)

    ; Open CSV file for writing
    csv_file = outfile(csv_filename)
    unless(csv_file
      printf("ERROR: Could not create CSV file: %s\n" csv_filename)
      return(nil)
    )

    ; Write CSV header with quotes around each field
    fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
            "PCB_Name" "Footprint_Name" "Place_Bound_Shapes" "Max_Height" "DFA_Bound_Shapes" "Display_Top_Added")

    ; Get components from collection
    components = collection["components"]

    ; Write each component's data
    foreach(component components
      fprintf(csv_file "\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n"
              component->pcb_name
              component->footprint_name
              if(component->place_bound_shape then sprintf(nil "%L" component->place_bound_shape) else "0")
              if(component->max_height then sprintf(nil "%f" component->max_height) else "0.0")
              if(component->dfa_bound_shape then sprintf(nil "%L" component->dfa_bound_shape) else "0")
              if(component->display_top then component->display_top else "no")
      )
    )

    ; Close the file
    close(csv_file)

    printf("Updated CSV file created: %s with %d components\n" csv_filename collection["count"])
    return(t)
  ); End Prog
); End procedure

; Load message
println("Save PCB Symbols to Library Script loaded. Run 'flir_footprint_update' to use.")