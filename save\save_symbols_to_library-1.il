;; =========================================================================
;; Save PCB Symbols to Library Script for Cadence Allegro
;; - Extracts all symbols from current PCB into "original_library" directory
;; - Creates directory if it doesn't exist
;; - <PERSON>les duplicate symbols
;; - Provides progress feedback during operation
;;
;; - We keep track of the PCB name and footprint name so we know which PCB file we 
;; - saved the footprint from. Either the variable Unique_Footprints is setto nil or 
;; - reads the csv file
;; - 
;; -  The format for the file name is :
;; -  1 = PCB FileName 
;; -  2 = footprint_name 
;; -  3 = Place_Bound_Shape - does the shape exist
;; -  4 = Max_Height - value or nil if it does not exist
;; -  5 = DFA_Bound_Shape - does the shape exist
;; -  6 = DISPLAY_TOP - did we create the shape on the display top layer. 
;; =========================================================================
;
; load "e:/pads/allegro/skill/ai/save_symbols_to_library.il"
;
;
; Explain how to use the structure, read it from a file or initialize it, add items and
; save it to a file.

; 1. First define the structure  - Create_File_structure()
; 2. Second create a component(this contains a pointer to a structure template) can use ->
;     to access the fields in a component - create_pcb_component(footprint_name pcb_name
;     place_bound max_height dfa_bound display_top)
; 3. Make a table so we can create and/or get components - create_component_collection() returns pointer
;     to a data structure that contains the structure (component) 
; 4. We can display the structure filed names and data - display_component(component)
; 5. We can update the fields in the structure  - update_component_field(component
;    Footprint_Key field_name new_value)
; 6. We can get the value of a field - get_component_field(component field_name)
;
; We not have a data structure called component and functions to write or read any fileds in the structure
; now we need to create a table to hold multiple structures of component
;
; 7. We now need to create a table that contains components and count to start -
;    create_component_collection() returns collection
; 8. Its time to read the CSV file if it exists.  load_collection_from_csv(filename) nil if no file
; 9. Ok. we have a table called collection to store data. 
; 10.Read and save footprints and update table. Go thru all footprints and then write the csv file. 
; 
; List functions to work with collection
;
; add_component_to_collection(collection component)
;
; get_collection_size(collection)
; 
; get_component_by_index(collection index)
;
; find_components_by_footprint(collection footprint_name)
;
; update_component_field(component field_name new_value)
;
; get_component_field(component field_name)
;
; 11.  Save the table to csv file



;********************************************************************
;
;	Structure routines
;
;********************************************************************

; Create_File_structure - creates a structure called PCB Component with the 7 items above. This is a template
; with the 7 values so you can adcess them by name. 
;
; When you define a structure SKILL creates the following functions
;
; make_pcb_component() - Constructor (creates new instance) - empty component. .
;
; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
;
; Field accessors - Access fields using arrow notation (->)


; pcb_component_p(obj) - Type checker (returns t if obj is pcb_component)
; Field accessors - Access fields using arrow notation (->)


; create_pcb_component(Footprint_name pcb_name place_bound max_height dfa_bound display_top
; - creates a variable and returns that variable called component(its local). It also calls 
;  
; make_pcb_component() is created by skill (see above).
;

;
; display_component(component) - displays the passed structure components 7 items
;



; update_component_field(component field_name new_value) - updates the provided field name with the new value
;  in teh passed component structure
;

 ;
; get_component_field(component field_name) gets the current field_name valur from the current component.
;  returns nil if no value to pass. 
;;
;; definitions for me
;;

;; 1  - structure   -  creates a group of the same variables so they can be used later.  
;; 2  - component   -  pointer to a structure
;; 3  - collection  - 

;;Think of it as:

;;collection = Base pointer to a control block
;;collection["components"] = Pointer to linked list of components
;;collection["count"] = Counter (like your COMPONENT_COUNT)

;; 4  - constructor - 
;; 5  - table -     - used to store multiple components


; =========================================================================
; STRUCTURE COLLECTION MANAGEMENT
; =========================================================================
;
;create_component_collection() - Makes a table called component_collection
; and sets it to collection
; set it components ??? and no count. 

;
;add_component_to_collection(collection component) - set collection to table and component 
; to values
;
;
; get_collection_size(collection) - gets the size of the passed collection
; 
;
; find_components_by_footprint(collection footprint_name)
;   

; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================
;
; save_collection_to_csv(collection filename)
;
; Routine to save the collection by adding a header and then each entry in a CSV file.
;

;
; display_collection_summary(collection)


;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================
;
; load_collection_from_csv(filename) - reads csv file into collection ansd returns the 
;  new variable
;
;
; import_csv_to_collection(existing_collection filename) - import and merge existing collection 
;  into csv file.
;
; parse_csv_line(line) - 

; 
; trim_string(str)
;
;
; create_sample_csv(filename)

;
; make_pcb_component
; 

axlCmdRegister("save_pcb_symbols_to_library" 'save_pcb_symbols_to_library)

;******************************************************
;
; Main Routine
;
;******************************************************


procedure(save_pcb_symbols_to_library()

  prog((target_dir footprint_names unique_symbols total_count success_count duplicate_count)
 
 
     duplicate_count = 0
     footprint_counter = 0

;
; Create a structure to use with the excel file and the footprints.
;

Create_File_structure()						
 
;
; Check if a design is currently open if not exit program
;
    unless(axlDBGetDesign()
        axlUIConfirm("No design is currently open.")
        println("No design is currently open.")
        return(nil)
    );End Unless
  ;
  ; Check if we're in the PCB Editor(not in symbol editor)
  ;
 
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    ); End unless
    
    ;
    ; Set target directory for saving symbols
    ;
    
    target_dir = "./original_library"
    
   ;
   ; Get PCB file name
   ;
   
   Pcb_File_Name = axlCurrentDesign()
   
   ;
   ; Get the design type
   ;
       design_type = axlDesignType(t)
      
   ;
   ; Get the working directory
   ;
       working_dir = getWorkingDir()
       
       ; Construct full path
    
    full_path = strcat(working_dir, "/" , Pcb_File_Name)
   
    
   ;
   ; If Directory for symbols does not exist create it and output message.
   ;
   
    if(createDir(target_dir) 
     then
      axlUIConfirm(sprintf(nil "Creating directory for symbols :  %s" target_dir))
      
     else
;     
; if Footprint_List_File exists then get file into a variable unique_footprints otherwise 
; create the structure for unique_fooptrints
; 

    if(isFile("Footprint_file_list.CSV") then 
   
     collection = load_collection_from_csv("Footprint_file_list.csv")
    else
     collection = create_component_collection()
     row_count = 0
     
    ); End If
   ); End If 

 
 
    ;
    ; Get all footprint names in the design. 
    ;
  
    footprint_names = axlDBGetDesign()->symbols~>name
   
   
    
 foreach(current_footprint_name footprint_names
 
  ;
 ; Check if this entry already exists as a value in the table
 ;
 
   if(find_components_by_footprint(collection current_footprint_name) then
 ;
 ; Entry already exists as a value, count as duplicate. change 
 ; Update pcb _name 
 ;
	duplicate_count = duplicate_count + 1
 
        update_component_field(component "pcb_name" Pcb_File_Name)
    
 ;
 ;	Add to collection 
 ;

   else 
; 
; Entry doesn't exist, add it to the table 
;
    component = create_pcb_component(current_footprint_name Pcb_File_Name nil nil nil nil)
    
    add_component_to_collection(collection component)

   );End If

 ); End Foreach
 
 ;
 ; Save collection to file
 ;

save_collection_to_csv(collection "Footprint_file_list.CSV")
display_collection_summary(collection  duplicate_count length(footprint_names))
break()


    
    
 ;  
 ;  
 ;
    
  
    ;
    ; Confirm operation with user
    ;
    unless(axlUIYesNo(sprintf(nil "Found %d unique symbols. Extract all to %s directory?" 
                             total_count target_dir))
      return(nil)
    )
    
    ; Process each unique symbol
    success_count = 0
    foreach(symbol_name unique_symbols
      component = unique_symbols[symbol_name]
      when(extract_symbol_to_library(component target_dir)
        success_count = success_count + 1
      )
      
      ; Update progress every 5 symbols
      when(remainder(success_count 5) == 0
        axlUIConfirm(sprintf(nil "Progress: %d of %d symbols processed" 
                            success_count total_count) nil)
      )
    )
    
    ; Report final results
    axlUIConfirm(sprintf(nil "Completed: %d of %d unique symbols saved to %s" 
                        success_count total_count target_dir))
    
       
  ); End Prog
); End Procedure

;******************************************************
;
; extract_symbol_to_library
;
;******************************************************


procedure(extract_symbol_to_library(component target_dir)
  prog((symbol_name symbol_path result)
    ;
    ; Get symbol name from component
    ;
    symbol_name = component->symbol~>name
    
    ; Skip if no symbol
    unless(symbol_name
      return(nil)
    ); End unless
    
    ; Create target path
    symbol_path = strcat(target_dir "/" symbol_name)
    
    ; Show progress
    println(sprintf(nil "Extracting symbol: %s" symbol_name))
    
    ; Extract the symbol
    result = nil
    
    ; Use axlExtractToFile to extract the symbol from the component
    result = axlExtractToFile(
      component,
      symbol_path,
      "SYMBOL")
      
      ; Extract as symbol
        
    ; Return result
    return(result)
  ); End Prog
);End 


;;; =========================================================================
;;; STRUCTURE DEFINITION
;;; =========================================================================

;******************************************************
;
; This is an initial create the initial structure for each entry
;
;
;******************************************************
procedure(Create_File_structure()




;
; Define the PCB component structure (only if not already defined)
;
;
; This creates a template for component data records
;

  defstruct(pcb_component
    pcb_name          ; PCB design name
    footprint_name    ; Component footprint name
    place_bound_shape ; Placement boundary shape data
    max_height        ; Maximum component height
    dfa_bound_shape   ; DFA boundary shape data
    display_top       ; Display top layer information
 ) 
  printf("PCB component structure defined\n")

); End Procedure

;******************************************************
;
; Function to create a new PCB component record
;
;******************************************************
procedure(create_pcb_component(footprint_name pcb_name place_bound max_height dfa_bound display_top)
  prog((component)
    ;
    ; Create new component structure instance
    ;
    component = make_pcb_component()

    ;
    ; Set all fields
    ;
    
    component->footprint_name = footprint_name
    component->pcb_name = pcb_name
    component->place_bound_shape = place_bound
    component->max_height = max_height
    component->dfa_bound_shape = dfa_bound
    component->display_top = display_top

    ;
    ; Return the created component
    ;
    return(component)
  )
)
;******************************************************
;
; Function to display component information
;
;******************************************************
procedure(display_component(component)
  prog(()
    printf("PCB Component Information:\n")
    printf("=========================\n")
     printf("Footprint Name:     %s\n" component->footprint_name)
   printf("PCB Name:           %s\n" component->pcb_name)
    printf("Place Bound Shape:  %s\n" component->place_bound_shape)
    printf("Max Height:         %s\n" component->max_height)
    printf("DFA Bound Shape:    %s\n" component->dfa_bound_shape)
    printf("Display Top:        %s\n" component->display_top)
    printf("\n")
  )
)

;******************************************************
;
; Function to update component fields
;
;******************************************************
procedure(update_component_field(component field_name new_value)
  prog(()
    case(field_name
      ("pcb_name"
        component->pcb_name = new_value)
      ("footprint_name"
        component->footprint_name = new_value)
      ("place_bound_shape"
        component->place_bound_shape = new_value)
      ("max_height"
        component->max_height = new_value)
      ("dfa_bound_shape"
        component->dfa_bound_shape = new_value)
      ("display_top"
        component->display_top = new_value)
      (t
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    ;printf("Updated %s to: %s\n" field_name new_value)
    return(t)
  )
)
;******************************************************
;
; Function to get component field value
;
;******************************************************
procedure(get_component_field(component field_name)
  prog((value)
    case(field_name
      ("pcb_name" 
        value = component->pcb_name)
      ("footprint_name" 
        value = component->footprint_name)
      ("place_bound_shape" 
        value = component->place_bound_shape)
      ("max_height" 
        value = component->max_height)
      ("dfa_bound_shape" 
        value = component->dfa_bound_shape)
      ("display_top" 
        value = component->display_top)
      (t 
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    return(value)
  )
)

;;; =========================================================================
;;; STRUCTURE COLLECTION MANAGEMENT
;;; =========================================================================

;
; Function to create a collection of components (using a table)
;
procedure(create_component_collection()
  prog((collection)
    ;
    ; Create table to hold multiple components
    ;
    collection = makeTable("component_collection" nil)
    collection["components"] = nil
    collection["count"] = 0

    ;
    ; Set structure field definitions for self-describing collection
    ;
    collection["structure_name"] = "pcb_component"
    collection["field_names"] = list(
      "pcb_name"
      "footprint_name"
      "place_bound_shape"
      "max_height"
      "dfa_bound_shape"
      "display_top"
    )
    collection["field_count"] = length(collection["field_names"])
    collection["csv_headers"] = list(
      "PCB Name"
      "Footprint Name"
      "Place Bound Shape"
      "Max Height"
      "DFA Bound Shape"
      "Display Top"
    )
    collection["version"] = "1.0"
    collection["created_date"] = getCurrentTime()

    return(collection)
  )
)

;
; Function to add component to collection
;
procedure(add_component_to_collection(collection component)
  prog((components)
    ;
    ; Get current components list
    ;
    components = collection["components"]

    ;
    ; Add new component
    ;
    if(components then
      collection["components"] = append(components list(component))
    else
      collection["components"] = list(component)
    )

    ;
    ; Update count
    ;
    collection["count"] = collection["count"] + 1

    printf("Added component to collection. Total count: %d\n" collection["count"])
    return(t)
  )
)

;
; Function to get collection size
;
procedure(get_collection_size(collection)
  return(collection["count"])
)

;
; Function to get component from collection by index
;
procedure(get_component_by_index(collection index)
  prog((components)
    components = collection["components"]

    ;
    ; Check bounds
    ;
    if(index < 1 || index > length(components) then
      printf("Index %d out of bounds (1 to %d)\n" index length(components))
      return(nil)
    )

    return(nthelem(index components))
  )
)

;
; Function to find components by footprint name
;
procedure(find_components_by_footprint(collection footprint_name)
  prog((components found_components)
    components = collection["components"]
    found_components = nil
    
    foreach(comp components
  
        when(comp->footprint_name == footprint_name
        found_components = cons(comp found_components)
      )
    )
 
    return(reverse(found_components))
  )
)
; =========================================================================
; CSV EXPORT FUNCTIONALITY
; =========================================================================

;******************************************************
;
; Function to save component collection to CSV file
;
;******************************************************

procedure(save_collection_to_csv(collection filename)
  prog((file_handle components)
    ;
    ; Open file for writing
    ;
    file_handle = outfile(filename "w")
    unless(file_handle
      printf("Failed to create CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Write CSV header
    ;
    fprintf(file_handle "PCB_Name,Footprint_Name,Place_Bound_Shape,Max_Height,DFA_Bound_Shape,Display_Top\n")

    ;
    ; Get components
    ;
    components = collection["components"]

    ;
    ; Write each component as CSV row
    ;
    foreach(comp components
      fprintf(file_handle "%s,%s,%s,%s,%s,%s\n"
        (if comp->pcb_name then comp->pcb_name else "")
        (if comp->footprint_name then comp->footprint_name else "")
        (if comp->place_bound_shape then comp->place_bound_shape else "")
        (if comp->max_height then comp->max_height else "")
        (if comp->dfa_bound_shape then comp->dfa_bound_shape else "")
        (if comp->display_top then comp->display_top else "")
      )
    )

    ;
    ; Close file
    ;
    close(file_handle)

    printf("Saved %d components to CSV file: %s\n" collection["count"] filename)
    return(t)
  )
)
;******************************************************

;
; Function to display collection summary
;
;******************************************************
procedure(display_collection_summary(collection duplicate_count total_footprints)
  prog((components unique_pcbs unique_footprints duplicate_count total_footprints)
 
    components = collection["components"]
    unique_pcbs = nil
    unique_footprints = nil
  
    printf("Component Collection Summary:\n")
    printf("============================\n")
    printf("Total unique Components: %d\n" collection["count"])

    ;
    ; Count unique PCBs and footprints
    ;
    foreach(comp components
      unless(member(comp->pcb_name unique_pcbs)
        unique_pcbs = cons(comp->pcb_name unique_pcbs)
      )
      unless(member(comp->footprint_name unique_footprints)
        unique_footprints = cons(comp->footprint_name unique_footprints)
      )
    )
    
    printf("Unique PCBs: %d\n" length(unique_pcbs))
    printf("Total Footprints: %d\n" total_footprints)
    printf("Total duplicate Footprints: %d\n" duplicate_count)
    printf("\n")
  )
)

;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================

;
; Enhanced function to load components from CSV file with proper CSV parsing
;
procedure(load_collection_from_csv(filename)
  prog((file_handle line collection fields comp row_count header_line)
    ;
    ; Check if file exists
    ;
    unless(isFile(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )

    ;
    ; Open file
    ;
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Create collection
    ;
    collection = create_component_collection()
    row_count = 0

    printf("Reading CSV file: %s\n" filename)

    ;
    ; Read header line
    ;
    if(gets(header_line file_handle) then
      printf("Header: %s\n" trim_string(header_line))
    )

    ;
    ; Read data lines
    ;
    while(gets(line file_handle)
      row_count = row_count + 1
      line = trim_string(line)

      ;
      ; Skip empty lines
      ;
      unless(strlen(line) == 0
        ;
        ; Parse CSV line with proper handling
        ;
        fields = parse_csv_line(line)

        ;
        ; Validate we have enough fields
        ;
break()
        if(length(fields) >= 6 then
          ;
          ; Create component from CSV data
          ;
          comp = create_pcb_component(
            trim_string(nthelem(1 fields))  ;; PCB Name
            trim_string(nthelem(2 fields))  ;; Footprint Name
            trim_string(nthelem(3 fields))  ;; Place Bound Shape
            trim_string(nthelem(4 fields))  ;; Max Height
            trim_string(nthelem(5 fields))  ;; DFA Bound Shape
            trim_string(nthelem(6 fields))  ;; Display Top
          )
          add_component_to_collection(collection comp)
          printf("  Row %d: Added %s - %s\n" row_count
                 comp->pcb_name comp->footprint_name)
        else
          printf("  Row %d: Skipped - insufficient fields (%d)\n"
                 row_count length(fields))
        )
      )
    )

    close(file_handle)
    printf("CSV import completed: %d components loaded from %d rows\n"
           collection["count"] row_count)
    return(collection)
  )
)

;
; Function to import and merge CSV data into existing collection
;
procedure(import_csv_to_collection(existing_collection filename)
  prog((imported_collection imported_components)
    ;
    ; Load CSV data
    ;
    imported_collection = load_collection_from_csv(filename)

    unless(imported_collection
      printf("Failed to import CSV data\n")
      return(nil)
    )

    ;
    ; Get imported components
    ;
    imported_components = imported_collection["components"]

    ;
    ; Add each imported component to existing collection
    ;
    foreach(comp imported_components
      add_component_to_collection(existing_collection comp)
    )

    printf("Merged %d components from CSV into existing collection\n"
           imported_collection["count"])
    return(existing_collection)
  )
)

;
; Function to parse CSV line with proper quote handling
;
procedure(parse_csv_line(line)
  prog((fields current_field in_quotes char i)
    fields = nil
    current_field = ""
    in_quotes = nil

    ;
    ; Process each character
    ;
    for(i 1 strlen(line)
      char = substring(line i i)

      cond(
        ;
        ; Handle quotes
        ;
        (equal(char "\"")
          if(in_quotes then
            ;
            ; Check for escaped quote
            ;
            if(i < strlen(line) && equal(substring(line (i+1) (i+1)) "\"") then
              current_field = strcat(current_field "\"")
              i = i + 1
            else
              in_quotes = nil
            )
          else
            in_quotes = t
          )
        )

        ;
        ; Handle comma
        ;
        (equal(char ",")
          if(in_quotes then
            current_field = strcat(current_field char)
          else
            fields = append(fields list(current_field))
            current_field = ""
          )
        )

        ;
        ; Regular character
        ;
        (t
          current_field = strcat(current_field char)
        )
      )
    )

    ;
    ; Add final field
    ;
    fields = append(fields list(current_field))
    return(fields)
  )
)

;
; Function to trim whitespace from string
;
procedure(trim_string(str)
  prog((start_pos end_pos result)
    unless(str
      return("")
    )

    ;
    ; Find first non-space character
    ;
    start_pos = 1
    while(start_pos <= strlen(str) &&
          member(substring(str start_pos start_pos) list(" " "\t" "\n" "\r"))
      start_pos = start_pos + 1
    )

    ;
    ; Find last non-space character
    ;
    end_pos = strlen(str)
    while(end_pos >= 1 &&
          member(substring(str end_pos end_pos) list(" " "\t" "\n" "\r"))
      end_pos = end_pos - 1
    )

    ;
    ; Extract trimmed string
    ;
    if(start_pos <= end_pos then
      result = substring(str start_pos end_pos)
    else
      result = ""
    )

    return(result)
  )
)



;
; Function to create sample CSV file for testing
;
procedure(create_sample_csv(filename)
  prog((file_handle)
    file_handle = outfile(filename "w")
    unless(file_handle
      printf("Failed to create sample CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Write header
    ;
    fprintf(file_handle "PCB_Name,Footprint_Name,Place_Bound_Shape,Max_Height,DFA_Bound_Shape,Display_Top\n")

    ;
    ; Write sample data
    ;
    fprintf(file_handle "MainBoard_v1.0,BGA_484,23.0x23.0,1.6mm,24.0x24.0,Visible\n")
    fprintf(file_handle "MainBoard_v1.0,QFN_64,9.0x9.0,0.9mm,10.0x10.0,Hidden\n")
    fprintf(file_handle "SensorBoard_v2.0,SOIC_16,10.3x3.9,1.75mm,11.0x4.5,Visible\n")
    fprintf(file_handle "PowerBoard_v1.5,TO-220,10.16x8.7,4.5mm,12.0x10.0,Visible\n")
    fprintf(file_handle "\"Test Board, Rev A\",\"BGA_256\",\"12.0x12.0\",\"1.2mm\",\"13.0x13.0\",\"Hidden\"\n")

    close(file_handle)
    printf("Created sample CSV file: %s\n" filename)
    return(t)
  )
)

;
; Enhanced demonstration with CSV import
;
; Load message
println("Save PCB Symbols to Library Script loaded. Run 'save_pcb_symbols_to_library' to use.")