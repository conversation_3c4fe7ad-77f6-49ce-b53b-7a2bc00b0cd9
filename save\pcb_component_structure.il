;;; =========================================================================
;;; PCB Component Structure Manager for Cadence Allegro
;;; - Defines structure for PCB component data
;;; - Provides functions to create, read, write, and manage component records
;;; - Includes CSV export functionality
;;; - Demonstrates structure operations and data management
;;; =========================================================================

axlCmdRegister("demo_pcb_structure" 'demo_pcb_structure)

;;; =========================================================================
;;; STRUCTURE DEFINITION
;;; =========================================================================

;
; Define the PCB component structure (only if not already defined)
;
;
; This creates a template for component data records
;
; Check if structure already exists before defining
when(not(boundp('make_pcb_component)))
  defstruct(pcb_component
    pcb_name          ; PCB design name
    footprint_name    ; Component footprint name
    place_bound_shape ; Placement boundary shape data
    max_height        ; Maximum component height
    dfa_bound_shape   ; DFA boundary shape data
    display_top       ; Display top layer information
  )
  printf("PCB component structure defined\n")
)

;;; =========================================================================
;;; STRUCTURE OPERATIONS
;;; =========================================================================

;
; Function to create a new PCB component record
;
procedure(create_pcb_component(pcb_name footprint_name place_bound max_height dfa_bound display_top)
  prog((component)
    ;
    ; Create new component structure instance
    ;
    component = make_pcb_component()

    ;
    ; Set all fields
    ;
    component->pcb_name = pcb_name
    component->footprint_name = footprint_name
    component->place_bound_shape = place_bound
    component->max_height = max_height
    component->dfa_bound_shape = dfa_bound
    component->display_top = display_top

    ;
    ; Return the created component
    ;
    return(component)
  )
)

;
; Function to display component information
;
procedure(display_component(component)
  prog()
    printf("PCB Component Information:\n")
    printf("=========================\n")
    printf("PCB Name:           %s\n" component->pcb_name)
    printf("Footprint Name:     %s\n" component->footprint_name)
    printf("Place Bound Shape:  %s\n" component->place_bound_shape)
    printf("Max Height:         %s\n" component->max_height)
    printf("DFA Bound Shape:    %s\n" component->dfa_bound_shape)
    printf("Display Top:        %s\n" component->display_top)
    printf("\n")
  )
)

;
; Function to update component fields
;
procedure(update_component_field(component field_name new_value)
  prog()
    case(field_name
      ("pcb_name"
        component->pcb_name = new_value)
      ("footprint_name"
        component->footprint_name = new_value)
      ("place_bound_shape"
        component->place_bound_shape = new_value)
      ("max_height"
        component->max_height = new_value)
      ("dfa_bound_shape"
        component->dfa_bound_shape = new_value)
      ("display_top"
        component->display_top = new_value)
      (t
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    printf("Updated %s to: %s\n" field_name new_value)
    return(t)
  )
)

;
; Function to get component field value
;
procedure(get_component_field(component field_name)
  prog((value)
    case(field_name
      ("pcb_name" 
        value = component->pcb_name)
      ("footprint_name" 
        value = component->footprint_name)
      ("place_bound_shape" 
        value = component->place_bound_shape)
      ("max_height" 
        value = component->max_height)
      ("dfa_bound_shape" 
        value = component->dfa_bound_shape)
      ("display_top" 
        value = component->display_top)
      (t 
        printf("Unknown field: %s\n" field_name)
        return(nil))
    )
    return(value)
  )
)

;;; =========================================================================
;;; STRUCTURE COLLECTION MANAGEMENT
;;; =========================================================================

;
; Function to create a collection of components (using a table)
;
procedure(create_component_collection()
  prog((collection)
    ;
    ; Create table to hold multiple components
    ;
    collection = makeTable("component_collection" nil)
    collection["components"] = nil
    collection["count"] = 0

    ;
    ; Set structure field definitions for self-describing collection
    ;
    collection["structure_name"] = "pcb_component"
    collection["field_names"] = list(
      "pcb_name"
      "footprint_name"
      "place_bound_shape"
      "max_height"
      "dfa_bound_shape"
      "display_top"
    )
    collection["field_count"] = length(collection["field_names"])
    collection["csv_headers"] = list(
      "PCB Name"
      "Footprint Name"
      "Place Bound Shape"
      "Max Height"
      "DFA Bound Shape"
      "Display Top"
    )
    collection["version"] = "1.0"
    collection["created_date"] = getCurrentTime()

    return(collection)
  )
)

;
; Function to add component to collection
;
procedure(add_component_to_collection(collection component)
  prog((components)
    ;
    ; Get current components list
    ;
    components = collection["components"]

    ;
    ; Add new component
    ;
    if(components then
      collection["components"] = append(components list(component))
    else
      collection["components"] = list(component)
    )

    ;
    ; Update count
    ;
    collection["count"] = collection["count"] + 1

    printf("Added component to collection. Total count: %d\n" collection["count"])
    return(t)
  )
)

;
; Function to get collection size
;
procedure(get_collection_size(collection)
  return(collection["count"])
)

;
; Function to get component from collection by index
;
procedure(get_component_by_index(collection index)
  prog((components)
    components = collection["components"]

    ;
    ; Check bounds
    ;
    if(index < 1 || index > length(components) then
      printf("Index %d out of bounds (1 to %d)\n" index length(components))
      return(nil)
    )

    return(nthelem(index components))
  )
)

;
; Function to find components by footprint name
;
procedure(find_components_by_footprint(collection footprint_name)
  prog((components found_components)
    components = collection["components"]
    found_components = nil
    
    foreach(comp components
      when(comp->footprint_name == footprint_name
        found_components = cons(comp found_components)
      )
    )
    
    return(reverse(found_components))
  )
)

;;; =========================================================================
;;; CSV EXPORT FUNCTIONALITY
;;; =========================================================================

;
; Function to save component collection to CSV file
;
procedure(save_collection_to_csv(collection filename)
  prog((file_handle components)
    ;
    ; Open file for writing
    ;
    file_handle = outfile(filename "w")
    unless(file_handle
      printf("Failed to create CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Write CSV header
    ;
    fprintf(file_handle "PCB_Name,Footprint_Name,Place_Bound_Shape,Max_Height,DFA_Bound_Shape,Display_Top\n")

    ;
    ; Get components
    ;
    components = collection["components"]

    ;
    ; Write each component as CSV row
    ;
    foreach(comp components
      fprintf(file_handle "%s,%s,%s,%s,%s,%s\n"
        (if comp->pcb_name then comp->pcb_name else "")
        (if comp->footprint_name then comp->footprint_name else "")
        (if comp->place_bound_shape then comp->place_bound_shape else "")
        (if comp->max_height then comp->max_height else "")
        (if comp->dfa_bound_shape then comp->dfa_bound_shape else "")
        (if comp->display_top then comp->display_top else "")
      )
    )

    ;
    ; Close file
    ;
    close(file_handle)

    printf("Saved %d components to CSV file: %s\n" collection["count"] filename)
    return(t)
  )
)

;
; Function to display collection summary
;
procedure(display_collection_summary(collection)
  prog((components unique_pcbs unique_footprints)
    components = collection["components"]
    unique_pcbs = nil
    unique_footprints = nil
    
    printf("Component Collection Summary:\n")
    printf("============================\n")
    printf("Total Components: %d\n" collection["count"])

    ;
    ; Count unique PCBs and footprints
    ;
    foreach(comp components
      unless(member(comp->pcb_name unique_pcbs)
        unique_pcbs = cons(comp->pcb_name unique_pcbs)
      )
      unless(member(comp->footprint_name unique_footprints)
        unique_footprints = cons(comp->footprint_name unique_footprints)
      )
    )
    
    printf("Unique PCBs: %d\n" length(unique_pcbs))
    printf("Unique Footprints: %d\n" length(unique_footprints))
    printf("\n")
  )
)

;;; =========================================================================
;;; SYSTEM INITIALIZATION
;;; =========================================================================

;
; Function to create and initialize the complete file structure system
;
procedure(create_file_structure()
  prog((system_info)
    printf("Initializing PCB Component Structure System\n")
    printf("==========================================\n")

    ;
    ; Create system information table
    ;
    system_info = makeTable("system_info" nil)
    system_info["version"] = "1.0"
    system_info["initialized"] = t
    system_info["component_count"] = 0
    system_info["collections"] = makeTable("collections" nil)

    printf("System initialized successfully\n")
    printf("Version: %s\n" system_info["version"])
    printf("Ready to create components and collections\n\n")

    return(system_info)
  )
)

;;; =========================================================================
;;; DEMONSTRATION FUNCTION
;;; =========================================================================

;
; Main demonstration function showing all operations
;
procedure(demo_pcb_structure()
  prog((collection comp1 comp2 comp3 found_components system_info)
    printf("PCB Component Structure Demonstration\n")
    printf("====================================\n\n")

    ;
    ; 0. Initialize file structure system
    ;
    printf("0. Initializing file structure system:\n")
    printf("--------------------------------------\n")
    system_info = create_file_structure()

    ;
    ; 1. Create individual components
    ;
    printf("1. Creating individual components:\n")
    printf("---------------------------------\n")

    comp1 = create_pcb_component(
      "MainBoard_v1.2"     ; PCB Name
      "BGA_256"            ; Footprint Name
      "12.0x12.0"          ; Place Bound Shape
      "1.2mm"              ; Max Height
      "13.0x13.0"          ; DFA Bound Shape
      "Visible"            ; Display Top
    )

    comp2 = create_pcb_component(
      "MainBoard_v1.2"     ; PCB Name
      "QFN_48"             ; Footprint Name
      "7.0x7.0"            ; Place Bound Shape
      "0.9mm"              ; Max Height
      "8.0x8.0"            ; DFA Bound Shape
      "Hidden"             ; Display Top
    )

    comp3 = create_pcb_component(
      "SensorBoard_v2.1"   ; PCB Name
      "BGA_256"            ; Footprint Name
      "12.0x12.0"          ; Place Bound Shape
      "1.5mm"              ; Max Height
      "13.5x13.5"          ; DFA Bound Shape
      "Visible"            ; Display Top
    )

    ;
    ; 2. Display individual components
    ;
    printf("2. Displaying component information:\n")
    printf("-----------------------------------\n")
    display_component(comp1)
    display_component(comp2)

    ;
    ; 3. Update component fields
    ;
    printf("3. Updating component fields:\n")
    printf("-----------------------------\n")
    update_component_field(comp1 "max_height" "1.4mm")
    update_component_field(comp2 "display_top" "Visible")

    ;
    ; 4. Read specific fields
    ;
    printf("4. Reading specific fields:\n")
    printf("---------------------------\n")
    printf("Component 1 PCB Name: %s\n" get_component_field(comp1 "pcb_name"))
    printf("Component 1 Max Height: %s\n" get_component_field(comp1 "max_height"))
    printf("Component 2 Footprint: %s\n" get_component_field(comp2 "footprint_name"))
    printf("\n")

    ;
    ; 5. Create collection and add components
    ;
    printf("5. Creating collection and adding components:\n")
    printf("--------------------------------------------\n")
    collection = create_component_collection()
    add_component_to_collection(collection comp1)
    add_component_to_collection(collection comp2)
    add_component_to_collection(collection comp3)

    ;
    ; 6. Show collection size
    ;
    printf("6. Collection size information:\n")
    printf("-------------------------------\n")
    printf("Collection size: %d components\n" get_collection_size(collection))
    display_collection_summary(collection)

    ;
    ; 7. Find components by footprint
    ;
    printf("7. Finding components by footprint:\n")
    printf("-----------------------------------\n")
    found_components = find_components_by_footprint(collection "BGA_256")
    printf("Found %d components with BGA_256 footprint:\n" length(found_components))
    foreach(comp found_components
      printf("  PCB: %s, Height: %s\n" comp->pcb_name comp->max_height)
    )
    printf("\n")

    ;
    ; 8. Access component by index
    ;
    printf("8. Accessing components by index:\n")
    printf("---------------------------------\n")
    comp_at_index = get_component_by_index(collection 2)
    if(comp_at_index then
      printf("Component at index 2:\n")
      display_component(comp_at_index)
    )

    ;
    ; 9. Save to CSV file
    ;
    printf("9. Saving collection to CSV file:\n")
    printf("---------------------------------\n")
    save_collection_to_csv(collection "pcb_components.csv")

    ;
    ; 10. Load from CSV file
    ;
    printf("10. Loading collection from CSV file:\n")
    printf("------------------------------------\n")
    loaded_collection = load_collection_from_csv("pcb_components.csv")
    if(loaded_collection then
      printf("Successfully loaded collection from CSV file\n")
      printf("Loaded collection size: %d components\n" get_collection_size(loaded_collection))
      display_collection_summary(loaded_collection)

      ;
      ; Verify loaded data matches original
      ;
      printf("Verifying loaded data:\n")
      first_loaded = get_component_by_index(loaded_collection 1)
      if(first_loaded then
        printf("First loaded component:\n")
        display_component(first_loaded)
      )
    else
      printf("Failed to load collection from CSV file\n")
    )

    ;
    ; 11. Create sample CSV file structure
    ;
    printf("11. Creating sample CSV file structure:\n")
    printf("--------------------------------------\n")
    create_sample_csv("sample_components.csv")

    ;
    ; 12. Comprehensive CSV import demonstration
    ;
    printf("12. Running comprehensive CSV import demo:\n")
    printf("-----------------------------------------\n")
    demo_csv_import()

    printf("Demonstration completed successfully!\n")
    return(collection)
  )
)

;;; =========================================================================
;;; UTILITY FUNCTIONS
;;; =========================================================================

;
; Enhanced function to load components from CSV file with proper CSV parsing
;
procedure(load_collection_from_csv(filename)
  prog((file_handle line collection fields comp row_count header_line)
    ;
    ; Check if file exists
    ;
    unless(isFile(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )

    ;
    ; Open file
    ;
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Create collection
    ;
    collection = create_component_collection()
    row_count = 0

    printf("Reading CSV file: %s\n" filename)

    ;
    ; Read header line
    ;
    if(gets(header_line file_handle) then
      printf("Header: %s\n" trim_string(header_line))
    )

    ;
    ; Read data lines
    ;
    while(gets(line file_handle)
      row_count = row_count + 1
      line = trim_string(line)

      ;
      ; Skip empty lines
      ;
      unless(strlen(line) == 0
        ;
        ; Parse CSV line with proper handling
        ;
        fields = parse_csv_line(line)

        ;
        ; Validate we have enough fields
        ;
        if(length(fields) >= 6 then
          ;
          ; Create component from CSV data
          ;
          comp = create_pcb_component(
            trim_string(nthelem(1 fields))  ;; PCB Name
            trim_string(nthelem(2 fields))  ;; Footprint Name
            trim_string(nthelem(3 fields))  ;; Place Bound Shape
            trim_string(nthelem(4 fields))  ;; Max Height
            trim_string(nthelem(5 fields))  ;; DFA Bound Shape
            trim_string(nthelem(6 fields))  ;; Display Top
          )
          add_component_to_collection(collection comp)
          printf("  Row %d: Added %s - %s\n" row_count
                 comp->pcb_name comp->footprint_name)
        else
          printf("  Row %d: Skipped - insufficient fields (%d)\n"
                 row_count length(fields))
        )
      )
    )

    close(file_handle)
    printf("CSV import completed: %d components loaded from %d rows\n"
           collection["count"] row_count)
    return(collection)
  )
)

;
; Function to import and merge CSV data into existing collection
;
procedure(import_csv_to_collection(existing_collection filename)
  prog((imported_collection imported_components)
    ;
    ; Load CSV data
    ;
    imported_collection = load_collection_from_csv(filename)

    unless(imported_collection
      printf("Failed to import CSV data\n")
      return(nil)
    )

    ;
    ; Get imported components
    ;
    imported_components = imported_collection["components"]

    ;
    ; Add each imported component to existing collection
    ;
    foreach(comp imported_components
      add_component_to_collection(existing_collection comp)
    )

    printf("Merged %d components from CSV into existing collection\n"
           imported_collection["count"])
    return(existing_collection)
  )
)

;
; Function to parse CSV line with proper quote handling
;
procedure(parse_csv_line(line)
  prog((fields current_field in_quotes char i)
    fields = nil
    current_field = ""
    in_quotes = nil

    ;
    ; Process each character
    ;
    for(i 1 strlen(line)
      char = substring(line i i)

      cond(
        ;
        ; Handle quotes
        ;
        (equal(char "\"")
          if(in_quotes then
            ;
            ; Check for escaped quote
            ;
            if(i < strlen(line) && equal(substring(line (i+1) (i+1)) "\"") then
              current_field = strcat(current_field "\"")
              i = i + 1
            else
              in_quotes = nil
            )
          else
            in_quotes = t
          )
        )

        ;
        ; Handle comma
        ;
        (equal(char ",")
          if(in_quotes then
            current_field = strcat(current_field char)
          else
            fields = append(fields list(current_field))
            current_field = ""
          )
        )

        ;
        ; Regular character
        ;
        (t
          current_field = strcat(current_field char)
        )
      )
    )

    ;
    ; Add final field
    ;
    fields = append(fields list(current_field))
    return(fields)
  )
)

;
; Function to trim whitespace from string
;
procedure(trim_string(str)
  prog((start_pos end_pos result)
    unless(str
      return("")
    )

    ;
    ; Find first non-space character
    ;
    start_pos = 1
    while(start_pos <= strlen(str) &&
          member(substring(str start_pos start_pos) list(" " "\t" "\n" "\r"))
      start_pos = start_pos + 1
    )

    ;
    ; Find last non-space character
    ;
    end_pos = strlen(str)
    while(end_pos >= 1 &&
          member(substring(str end_pos end_pos) list(" " "\t" "\n" "\r"))
      end_pos = end_pos - 1
    )

    ;
    ; Extract trimmed string
    ;
    if(start_pos <= end_pos then
      result = substring(str start_pos end_pos)
    else
      result = ""
    )

    return(result)
  )
)

;
; Helper function to check if file exists
;
procedure(isFile(filename)
  prog((file_handle)
    file_handle = infile(filename)
    if(file_handle then
      close(file_handle)
      return(t)
    else
      return(nil)
    )
  )
)

;
; Function to create sample CSV file for testing
;
procedure(create_sample_csv(filename)
  prog((file_handle)
    file_handle = outfile(filename "w")
    unless(file_handle
      printf("Failed to create sample CSV file: %s\n" filename)
      return(nil)
    )

    ;
    ; Write header
    ;
    fprintf(file_handle "PCB_Name,Footprint_Name,Place_Bound_Shape,Max_Height,DFA_Bound_Shape,Display_Top\n")

    ;
    ; Write sample data
    ;
    fprintf(file_handle "MainBoard_v1.0,BGA_484,23.0x23.0,1.6mm,24.0x24.0,Visible\n")
    fprintf(file_handle "MainBoard_v1.0,QFN_64,9.0x9.0,0.9mm,10.0x10.0,Hidden\n")
    fprintf(file_handle "SensorBoard_v2.0,SOIC_16,10.3x3.9,1.75mm,11.0x4.5,Visible\n")
    fprintf(file_handle "PowerBoard_v1.5,TO-220,10.16x8.7,4.5mm,12.0x10.0,Visible\n")
    fprintf(file_handle "\"Test Board, Rev A\",\"BGA_256\",\"12.0x12.0\",\"1.2mm\",\"13.0x13.0\",\"Hidden\"\n")

    close(file_handle)
    printf("Created sample CSV file: %s\n" filename)
    return(t)
  )
)

;
; Enhanced demonstration with CSV import
;
procedure(demo_csv_import()
  prog((collection sample_file imported_collection)
    printf("CSV Import Demonstration\n")
    printf("=======================\n\n")

    ;
    ; 1. Create sample CSV file
    ;
    printf("1. Creating sample CSV file:\n")
    printf("----------------------------\n")
    sample_file = "sample_components.csv"
    create_sample_csv(sample_file)

    ;
    ; 2. Load components from CSV
    ;
    printf("\n2. Loading components from CSV:\n")
    printf("-------------------------------\n")
    imported_collection = load_collection_from_csv(sample_file)

    if(imported_collection then
      printf("\n3. Displaying imported components:\n")
      printf("----------------------------------\n")
      display_collection_summary(imported_collection)

      ;
      ; Show first few components
      ;
      for(i 1 min(3 imported_collection["count"])
        comp = get_component_by_index(imported_collection i)
        printf("Component %d:\n" i)
        display_component(comp)
      )
    )

    ;
    ; 4. Demonstrate merging with existing collection
    ;
    printf("4. Merging with existing collection:\n")
    printf("------------------------------------\n")

    ;
    ; Create a collection with manual components
    ;
    collection = create_component_collection()
    manual_comp = create_pcb_component(
      "ManualBoard_v1.0" "TQFP_100" "14.0x14.0" "1.4mm" "15.0x15.0" "Visible"
    )
    add_component_to_collection(collection manual_comp)

    printf("Original collection size: %d\n" get_collection_size(collection))

    ;
    ; Import and merge CSV data
    ;
    import_csv_to_collection(collection sample_file)
    printf("Final collection size: %d\n" get_collection_size(collection))

    printf("\nCSV import demonstration completed!\n")
    return(collection)
  )
)

;
; Load message
;
println("PCB Component Structure Manager loaded.")
println("Commands available:")
println("  demo_pcb_structure              - Run complete demonstration")
println("  demo_csv_import                 - Demonstrate CSV import functionality")
println("  create_pcb_component(...)       - Create new component")
println("  create_component_collection()   - Create component collection")
println("  save_collection_to_csv(...)     - Save collection to CSV")
println("  load_collection_from_csv(...)   - Load collection from CSV")
println("  import_csv_to_collection(...)   - Import CSV into existing collection")
println("  create_sample_csv(...)          - Create sample CSV file for testing")
println("")
println("Structure fields:")
println("  pcb_name, footprint_name, place_bound_shape")
println("  max_height, dfa_bound_shape, display_top")
println("")
println("Usage examples:")
println("  collection = load_collection_from_csv(\"components.csv\")")
println("  import_csv_to_collection(existing_collection \"new_data.csv\")")
println("  create_sample_csv(\"test.csv\")")
println("")
println("Run 'demo_pcb_structure' or 'demo_csv_import' to see operations!")
