;; =========================================================================
;; Load and Save DRA Files Script for Cadence Allegro
;; - Finds all *.dra files in a specified directory
;; - Opens each .dra file in Allegro
;; - Saves it back to the same directory (refreshes the file)
;; - Closes the file before moving to the next
;; =========================================================================

axlCmdRegister("load_save_dra_files" 'load_save_dra_files)

procedure(load_save_dra_files()
  prog((form_id)
    ; Create form to get directory path
    form_id = axlFormCreate(
      (list
        (list "text" "Directory containing .dra files:")
        (list "field" "dra_dir" 50 "./")
        (list "text" " ")
        (list "text" "This will load and save each .dra file in the directory.")
      )
      "Load and Save DRA Files" 
      (list "OK" "Cancel")
      'load_save_form_callback
      t
    )
    
    ; Display the form
    axlFormDisplay(form_id)
    
    return(t)
  )
)

procedure(load_save_form_callback(form_id)
  prog((dra_dir dra_files processed_count failed_count)
    ; Get directory from form
    dra_dir = axlFormGetField(form_id "dra_dir")
    
    ; Close the form
    axlFormClose(form_id)
    
    ; Validate directory
    unless(isDir(dra_dir)
      axlUIConfirm(sprintf(nil "Directory does not exist: %s" dra_dir))
      return(nil)
    )
    
    ; Get all .dra files in directory
    dra_files = getDraFiles(dra_dir)
    
    ; Check if any .dra files found
    if(length(dra_files) == 0 then
      axlUIConfirm(sprintf(nil "No .dra files found in directory: %s" dra_dir))
      return(nil)
    )
    
    ; Confirm with user
    unless(axlUIConfirm(sprintf(nil "Found %d .dra files in %s\n\nProceed to load and save each file?" 
                               length(dra_files) dra_dir))
      return(nil)
    )
    
    ; Initialize counters
    processed_count = 0
    failed_count = 0
    
    ; Process each .dra file
    foreach(dra_file dra_files
      if(load_and_save_dra_file(dra_file) then
        processed_count = processed_count + 1
      else
        failed_count = failed_count + 1
      )
    )
    
    ; Report results
    axlUIConfirm(sprintf(nil "Processing Complete!\n\nSuccessfully processed: %d files\nFailed: %d files\nTotal: %d files" 
                        processed_count failed_count length(dra_files)))
    
    return(t)
  )
)

procedure(load_and_save_dra_file(dra_file)
  prog((result)
    ; Display current file being processed
    println(sprintf(nil "Processing: %s" dra_file))
    
    ; Close current design if open
    when(axlDBGetDesign()
      axlCloseDesign()
    )
    
    ; Open the .dra file
    result = axlOpenDesign(dra_file)
    unless(result
      println(sprintf(nil "ERROR: Failed to open: %s" dra_file))
      return(nil)
    )
    
    ; Verify design opened successfully
    unless(axlDBGetDesign()
      println(sprintf(nil "ERROR: Design not properly loaded: %s" dra_file))
      return(nil)
    )
    
    println(sprintf(nil "  Opened: %s" dra_file))
    
    ; Save the design back to same location
    result = axlSaveDesign()
    unless(result
      println(sprintf(nil "ERROR: Failed to save: %s" dra_file))
      axlCloseDesign()
      return(nil)
    )
    
    println(sprintf(nil "  Saved: %s" dra_file))
    
    ; Close the design
    axlCloseDesign()
    println(sprintf(nil "  Closed: %s" dra_file))
    
    return(t)
  )
)

procedure(getDraFiles(dir)
  prog((all_files dra_files)
    ; Get all files in directory
    all_files = getDirFiles(dir)
    
    ; Filter for .dra files
    dra_files = nil
    foreach(file all_files
      when(stringEndsWith(lowerCase(file) ".dra")
        dra_files = cons(strcat(dir "/" file) dra_files)
      )
    )
    
    return(reverse(dra_files))
  )
)

; Helper function to check if string ends with suffix
procedure(stringEndsWith(str suffix)
  prog((str_len suffix_len)
    str_len = strlen(str)
    suffix_len = strlen(suffix)
    
    if(str_len < suffix_len then
      return(nil)
    else
      return(equal(substring(str (str_len - suffix_len + 1) str_len) suffix))
    )
  )
)

; Helper function to check if directory exists
procedure(isDir(dir)
  prog((result)
    result = nil
    
    ; Try to check if it's a directory by attempting to get files
    if(isFile(dir) then
      ; It's a file, not a directory
      result = nil
    else
      ; Check if directory exists by trying to get files
      result = (getDirFiles(dir) != nil)
    )
    
    return(result)
  )
)

; Load message
println("Load and Save DRA Files Script loaded.")
println("Run 'load_save_dra_files' to use.")
println("")
println("This script will:")
println("  1. Ask for a directory path")
println("  2. Find all *.dra files in that directory")
println("  3. Open each .dra file")
println("  4. Save it back to the same location")
println("  5. Close the file and move to the next")
