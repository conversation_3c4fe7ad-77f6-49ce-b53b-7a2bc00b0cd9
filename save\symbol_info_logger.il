;; =========================================================================
;; Symbol Info Logger Script for Cadence Allegro
;; - Checks for symbol_info spreadsheet in current directory
;; - Creates it with headers if it doesn't exist
;; - Adds a new row with symbol information
;; - Tracks current row for future additions
;; =========================================================================

axlCmdRegister("log_symbol_info" 'log_symbol_info)

procedure(log_symbol_info()
  prog((spreadsheet_file current_row file_exists file_handle lines)
    ; Set spreadsheet filename
    spreadsheet_file = "symbol_info.csv"
    
    ; Check if file exists
    file_exists = isFile(spreadsheet_file)
    
    ; If file doesn't exist, create it with headers
    unless(file_exists
      file_handle = outfile(spreadsheet_file "w")
      fprintf(file_handle "Footprint Name,Display_TOP data deleted,DFA_BOUND shape exists,DISPLAY_TOP Created,PLACE_BOUND_TOP exists,Component Height\n")
      close(file_handle)
      current_row = 2
      println(sprintf(nil "Created new spreadsheet: %s" spreadsheet_file))
    else
      ; File exists, find the next blank row
      current_row = get_next_row(spreadsheet_file)
      println(sprintf(nil "Using existing spreadsheet: %s (next row: %d)" spreadsheet_file current_row))
    )
    
    ; Append new data row
    file_handle = outfile(spreadsheet_file "a")
    fprintf(file_handle "SOD123,Yes,Yes,Yes,Yes,100\n")
    close(file_handle)
    
    ; Save current row for future use
    save_current_row(current_row + 1)
    
    ; Confirm to user
    axlUIConfirm(sprintf(nil "Added data to %s at row %d" spreadsheet_file current_row))
    
    return(t)
  )
)

procedure(get_next_row(filename)
  prog((file_handle line row_count)
    ; Open file for reading
    file_handle = infile(filename)
    unless(file_handle
      return(2)  ; Default to row 2 if can't open file
    )
    
    ; Count lines in file
    row_count = 0
    while(gets(line file_handle)
      row_count = row_count + 1
    )
    
    ; Close file
    close(file_handle)
    
    ; Check if we have a saved current row that's higher
    saved_row = get_saved_current_row()
    when(saved_row > row_count + 1
      row_count = saved_row - 1
    )
    
    ; Return next row (row count + 1)
    return(row_count + 1)
  )
)

procedure(save_current_row(row)
  prog((file_handle)
    ; Save current row to a tracking file
    file_handle = outfile("symbol_info_row.txt" "w")
    fprintf(file_handle "%d\n" row)
    close(file_handle)
    return(t)
  )
)

procedure(get_saved_current_row()
  prog((file_handle row)
    ; Default row if file doesn't exist
    row = 2
    
    ; Try to read saved row
    when(isFile("symbol_info_row.txt")
      file_handle = infile("symbol_info_row.txt")
      when(file_handle
        gets(line file_handle)
        row = atoi(line)
        close(file_handle)
      )
    )
    
    return(row)
  )
)

; Helper function to check if file exists
procedure(isFile(filename)
  prog((file_handle)
    file_handle = infile(filename)
    if(file_handle then
      close(file_handle)
      return(t)
    else
      return(nil)
    )
  )
)

; Load message
println("Symbol Info Logger Script loaded. Run 'log_symbol_info' to use.")