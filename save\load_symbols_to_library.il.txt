;; =========================================================================
;; Load Symbols to Library Script for Cadence Allegro
;; - Loads all symbols from specified directories into "old_library" directory
;; - Preserves symbol properties and attributes
;; - Provides progress feedback during operation
;; =========================================================================
;
; load "e:/pads/allegro/skill/ai/load_symbols_to_library.il"
axlCmdRegister("load_symbols_to_library" 'load_symbols_to_library)

procedure(load_symbols_to_library()
  prog((form_id source_dirs target_dir symbol_files total_count success_count)
    ; Create the form for user input
    form_id = axlFormCreate(
      (list
        (list "text" "Source Directories (separate with semicolons):")
        (list "field" "source_dirs" 40)
        (list "text" "Target Library Directory:")
        (list "field" "target_dir" 40 "old_library")
      )
      "Load Symbols to Library" 
      (list "OK" "Cancel")
      'load_symbols_form_callback
      t
    )
    
    ; Display the form
    axlFormDisplay(form_id)
    
    return(t)
  )
)

procedure(load_symbols_form_callback(form)
  prog((source_dirs target_dir)
    if(form->curField == "OK" then
      ; Get input values
      source_dirs = parseString(form->source_dirs ";")
      target_dir = form->target_dir
      
      ; Ensure target directory exists
      unless(isDir(target_dir)
        axlUIConfirm(sprintf(nil "Creating directory: %s" target_dir))
        system(sprintf(nil "mkdir -p %s" target_dir))
      )
      
      ; Process each source directory
      process_directories(source_dirs target_dir)
    )
    
    ; Close form
    axlFormClose(form)
    return(t)
  )
)

procedure(process_directories(source_dirs target_dir)
  prog((total_count success_count)
    total_count = 0
    success_count = 0
    
    ; Process each directory
    foreach(dir source_dirs
      when(isDir(dir)
        ; Get all symbol files in directory
        symbol_files = get_symbol_files(dir)
        total_count = total_count + length(symbol_files)
        
        ; Process each symbol file
        foreach(symbol_file symbol_files
          when(copy_symbol_to_library(symbol_file target_dir)
            success_count = success_count + 1
          )
          
          ; Update progress every 10 symbols
          when(remainder(success_count 10) == 0
            axlUIConfirm(sprintf(nil "Progress: %d of %d symbols processed" 
                                success_count total_count) nil)
          )
        )
      )
    )
    
    ; Report final results
    axlUIConfirm(sprintf(nil "Completed: %d of %d symbols loaded to %s" 
                        success_count total_count target_dir))
    
    return(t)
  )
)

procedure(get_symbol_files(dir)
  prog((files symbol_files)
    ; Get all files in directory
    files = getDirFiles(dir)
    
    ; Filter for symbol files (.psm, .bsm, .ssm, .osm)
    symbol_files = nil
    foreach(file files
      when(or(stringEndsWith(lowerCase(file) ".psm")
              stringEndsWith(lowerCase(file) ".bsm")
              stringEndsWith(lowerCase(file) ".ssm")
              stringEndsWith(lowerCase(file) ".osm"))
        symbol_files = cons(strcat(dir "/" file), symbol_files)
      )
    )
    
    return(symbol_files)
  )
)

procedure(copy_symbol_to_library(symbol_file target_dir)
  prog((symbol_name result)
    ; Extract symbol name from file path
    symbol_name = car(last(parseString(symbol_file "/")))
    
    ; Create target path
    target_path = strcat(target_dir "/" symbol_name)
    
    ; Copy the symbol file
    result = nil
    axlUIConfirm(sprintf(nil "Loading symbol: %s" symbol_name) nil)
    
    ; Try to load the symbol
    result = axlLoadSymbol(symbol_file)
    
    ; If loaded successfully, save to target directory
    when(result
      result = axlSaveSymbol(target_path)
    )
    
    return(result)
  )
)

; Helper function to check if string ends with suffix
procedure(stringEndsWith(str suffix)
  prog((str_len suffix_len)
    str_len = strlen(str)
    suffix_len = strlen(suffix)
    
    if(str_len < suffix_len then
      return(nil)
    else
      return(equal(substring(str (str_len - suffix_len + 1) str_len) suffix))
    )
  )
)

; Load message
println("Load Symbols to Library Script loaded. Run 'load_symbols_to_library' to use.")